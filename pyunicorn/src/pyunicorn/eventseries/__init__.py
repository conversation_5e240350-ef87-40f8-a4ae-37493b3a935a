# This file is part of pyunicorn.
# Copyright (C) 2008--2025 <PERSON> and pyunicorn authors
# URL: <https://www.pik-potsdam.de/members/donges/software-2/software>
# License: BSD (3-clause)
#
# Please acknowledge and cite the use of this software and its authors
# when results are used in publications or published elsewhere.
#
# You can use the following reference:
# <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Q.-<PERSON><PERSON>,
# <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,
# and <PERSON><PERSON>, "Unified functional network and nonlinear time series analysis
# for complex systems science: The pyunicorn package"

"""
eventseries
===========

Provides a class for analyzing event series, namely event synchronization and
event coincidence analysis

Related Publications
~~~~~~~~~~~~~~~~~~~~
[Quiroga2002]_, [Boers2014]_, [<PERSON><PERSON>2016]_, [Odenweller2020]_, [Kreuz2007]_,
[<PERSON><PERSON>2015]_, [<PERSON><PERSON><PERSON><PERSON>2016]_.

To do
~~~~~
  - Combine precursor and trigger coincidence rate to obtain one ECA measure
"""

from .event_series import EventSeries
