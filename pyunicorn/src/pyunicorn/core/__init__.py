# This file is part of pyunicorn.
# Copyright (C) 2008--2025 <PERSON> and pyunicorn authors
# URL: <https://www.pik-potsdam.de/members/donges/software-2/software>
# License: BSD (3-clause)
#
# Please acknowledge and cite the use of this software and its authors
# when results are used in publications or published elsewhere.
#
# You can use the following reference:
# <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Q.<PERSON><PERSON><PERSON>,
# <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,
# and <PERSON><PERSON>, "Unified functional network and nonlinear time series analysis
# for complex systems science: The pyunicorn package"

"""
core
====

Provides classes for analyzing spatially embedded complex networks, handling
multivariate data and generating time series surrogates.

Related Publications
~~~~~~~~~~~~~~~~~~~~
[Donges2011a]_, [He<PERSON>2012]_, [Donges2012]_
"""

#
#  Import classes
#

from .network import Network, NetworkError, nz_coords
from .spatial_network import SpatialNetwork
from .geo_network import GeoNetwork
from .grid import Grid
from .geo_grid import GeoGrid
from .data import Data
from .interacting_networks import InteractingNetworks
from .netcdf_dictionary import NetCDFDictionary
from .resistive_network import ResNetwork

#
#  Set global constants
#

#  Mean earth radius in kilometers
EARTH_RADIUS = 6367.5
"""(float) - The earth's mean radius in kilometers."""
