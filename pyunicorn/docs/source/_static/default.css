/**
 * Alternate Sphinx design
 * Originally created by <PERSON><PERSON> for Werkzeug, adapted by <PERSON>.
 */

body {
    font-family: 'Lucida Grande', 'Lucida Sans Unicode', 'Geneva', 'Verdana', sans-serif;
    font-size: 14px;
    letter-spacing: -0.01em;
    line-height: 150%;
    text-align: center;
    /*background-color: #AFC1C4; */
    background-color: #BFD1D4;
    color: black;
    padding: 0;
    border: 1px solid #aaa;

    margin: 0px 80px 0px 80px;
    min-width: 740px;
}

a {
    color: #CA7900;
    text-decoration: none;
}

a:hover {
    color: #2491CF;
}

pre {
    font-family: 'Consolas', 'Deja Vu Sans Mono', 'Bitstream Vera Sans Mono', monospace;
    font-size: 0.95em;
    letter-spacing: 0.015em;
    padding: 0.5em;
    border: 1px solid #ccc;
    background-color: #f8f8f8;
}

td.linenos pre {
    padding: 0.5em 0;
    border: 0;
    background-color: transparent;
    color: #aaa;
}

table.highlighttable {
    margin-left: 0.5em;
}

table.highlighttable td {
    padding: 0 0.5em 0 0.5em;
}

cite, code, tt {
    font-family: '<PERSON>sol<PERSON>', 'Deja Vu Sans Mono', 'Bitstream Vera Sans Mono', monospace;
    font-size: 0.95em;
    letter-spacing: 0.01em;
}

hr {
    border: 1px solid #abc;
    margin: 2em;
}

tt {
    background-color: #f2f2f2;
    border-bottom: 1px solid #ddd;
    color: #333;
}

tt.descname {
    background-color: transparent;
    font-weight: bold;
    font-size: 1.2em;
    border: 0;
}

tt.descclassname {
    background-color: transparent;
    border: 0;
}

tt.xref {
    background-color: transparent;
    font-weight: bold;
    border: 0;
}

a tt {
    background-color: transparent;
    font-weight: bold;
    border: 0;
    color: #CA7900;
}

a tt:hover {
    color: #2491CF;
}

dl {
    margin-bottom: 15px;
}

dd p {
    margin-top: 0px;
}

dd ul, dd table {
    margin-bottom: 10px;
}

dd {
    margin-top: 3px;
    margin-bottom: 10px;
    margin-left: 30px;
}

.refcount {
    color: #060;
}

dt:target,
.highlight {
    background-color: #fbe54e;
}

dl.class, dl.function {
    border-top: 2px solid #888;
}

dl.method, dl.attribute {
    border-top: 1px solid #aaa;
}

dl.glossary dt {
    font-weight: bold;
    font-size: 1.1em;
}

pre {
    line-height: 120%;
}

pre a {
    color: inherit;
    text-decoration: underline;
}

.first {
    margin-top: 0 !important;
}

div.document {
    background-color: white;
    text-align: left;
    background-image: url(contents.png);
    background-repeat: repeat-x;
}

/*
div.documentwrapper {
    width: 100%;
}
*/

div.clearer {
    clear: both;
}

div.related h3 {
    display: none;
}

div.related ul {
    background-image: url(navigation.png);
    height: 2em;
    list-style: none;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    margin: 0;
    padding-left: 10px;
}

div.related ul li {
    margin: 0;
    padding: 0;
    height: 2em;
    float: left;
}

div.related ul li.right {
    float: right;
    margin-right: 5px;
}

div.related ul li a {
    margin: 0;
    padding: 0 5px 0 5px;
    line-height: 1.75em;
    color: #EE9816;
}

div.related ul li a:hover {
    color: #3CA8E7;
}

div.body {
    margin: 0;
    padding: 0.5em 20px 20px 20px;
}

div.bodywrapper {
    margin: 0 240px 0 0;
    border-right: 1px solid #ccc;
}

div.body a {
    text-decoration: underline;
}

div.sphinxsidebar {
    margin: 0;
    padding: 0.5em 15px 15px 0;
    width: 210px;
    float: right;
    text-align: left;
/*    margin-left: -100%; */
}

div.sphinxsidebar h4, div.sphinxsidebar h3 {
    margin: 1em 0 0.5em 0;
    font-size: 0.9em;
    padding: 0.1em 0 0.1em 0.5em;
    color: white;
    border: 1px solid #86989B;
    background-color: #AFC1C4;
}

div.sphinxsidebar ul {
    padding-left: 1.5em;
    margin-top: 7px;
    list-style: none;
    padding: 0;
    line-height: 130%;
}

div.sphinxsidebar ul ul {
    list-style: square;
    margin-left: 20px;
}

p {
    margin: 0.8em 0 0.5em 0;
}

p.rubric {
    font-weight: bold;
}

h1 {
    margin: 0;
    padding: 0.7em 0 0.3em 0;
    font-size: 1.5em;
    color: #11557C;
}

h2 {
    margin: 1.3em 0 0.2em 0;
    font-size: 1.35em;
    padding: 0;
}

h3 {
    margin: 1em 0 -0.3em 0;
    font-size: 1.2em;
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
    color: black!important;
}

h1 a.anchor, h2 a.anchor, h3 a.anchor, h4 a.anchor, h5 a.anchor, h6 a.anchor {
    display: none;
    margin: 0 0 0 0.3em;
    padding: 0 0.2em 0 0.2em;
    color: #aaa!important;
}

h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor,
h5:hover a.anchor, h6:hover a.anchor {
    display: inline;
}

h1 a.anchor:hover, h2 a.anchor:hover, h3 a.anchor:hover, h4 a.anchor:hover,
h5 a.anchor:hover, h6 a.anchor:hover {
    color: #777;
    background-color: #eee;
}

table {
    border-collapse: collapse;
    margin: 0 -0.5em 0 -0.5em;
}

table td, table th {
    padding: 0.2em 0.5em 0.2em 0.5em;
}

div.footer {
    background-color: #E3EFF1;
    color: #86989B;
    padding: 3px 8px 3px 0;
    clear: both;
    font-size: 0.8em;
    text-align: right;
}

div.footer a {
    color: #86989B;
    text-decoration: underline;
}

div.pagination {
    margin-top: 2em;
    padding-top: 0.5em;
    border-top: 1px solid black;
    text-align: center;
}

div.sphinxsidebar ul.toc {
    margin: 1em 0 1em 0;
    padding: 0 0 0 0.5em;
    list-style: none;
}

div.sphinxsidebar ul.toc li {
    margin: 0.5em 0 0.5em 0;
    font-size: 0.9em;
    line-height: 130%;
}

div.sphinxsidebar ul.toc li p {
    margin: 0;
    padding: 0;
}

div.sphinxsidebar ul.toc ul {
    margin: 0.2em 0 0.2em 0;
    padding: 0 0 0 1.8em;
}

div.sphinxsidebar ul.toc ul li {
    padding: 0;
}

div.admonition, div.warning {
    font-size: 0.9em;
    margin: 1em 0 0 0;
    border: 1px solid #86989B;
    background-color: #f7f7f7;
}

div.admonition p, div.warning p {
    margin: 0.5em 1em 0.5em 1em;
    padding: 0;
}

div.admonition pre, div.warning pre {
    margin: 0.4em 1em 0.4em 1em;
}

div.admonition p.admonition-title,
div.warning p.admonition-title {
    margin: 0;
    padding: 0.1em 0 0.1em 0.5em;
    color: white;
    border-bottom: 1px solid #86989B;
    font-weight: bold;
    background-color: #AFC1C4;
}

div.warning {
    border: 1px solid #940000;
}

div.warning p.admonition-title {
    background-color: #CF0000;
    border-bottom-color: #940000;
}

div.admonition ul, div.admonition ol,
div.warning ul, div.warning ol {
    margin: 0.1em 0.5em 0.5em 3em;
    padding: 0;
}

div.versioninfo {
    margin: 1em 0 0 0;
    border: 1px solid #ccc;
    background-color: #DDEAF0;
    padding: 8px;
    line-height: 1.3em;
    font-size: 0.9em;
}


a.headerlink {
    color: #c60f0f!important;
    font-size: 1em;
    margin-left: 6px;
    padding: 0 4px 0 4px;
    text-decoration: none!important;
    visibility: hidden;
}

h1:hover > a.headerlink,
h2:hover > a.headerlink,
h3:hover > a.headerlink,
h4:hover > a.headerlink,
h5:hover > a.headerlink,
h6:hover > a.headerlink,
dt:hover > a.headerlink {
    visibility: visible;
}

a.headerlink:hover {
    background-color: #ccc;
    color: white!important;
}

table.indextable td {
    text-align: left;
    vertical-align: top;
}

table.indextable dl, table.indextable dd {
    margin-top: 0;
    margin-bottom: 0;
}

table.indextable tr.pcap {
    height: 10px;
}

table.indextable tr.cap {
    margin-top: 10px;
    background-color: #f2f2f2;
}

img.toggler {
    margin-right: 3px;
    margin-top: 3px;
    cursor: pointer;
}

img.inheritance {
    border: 0px
}

form.pfform {
    margin: 10px 0 20px 0;
}

table.contentstable {
    width: 90%;
}

table.contentstable p.biglink {
    line-height: 150%;
}

a.biglink {
    font-size: 1.3em;
}

span.linkdescr {
    font-style: italic;
    padding-top: 5px;
    font-size: 90%;
}

ul.search {
    margin: 10px 0 0 20px;
    padding: 0;
}

ul.search li {
    padding: 5px 0 5px 20px;
    background-image: url(file.png);
    background-repeat: no-repeat;
    background-position: 0 7px;
}

ul.search li a {
    font-weight: bold;
}

ul.search li div.context {
    color: #888;
    margin: 2px 0 0 30px;
    text-align: left;
}

ul.keywordmatches li.goodmatch a {
    font-weight: bold;
}
