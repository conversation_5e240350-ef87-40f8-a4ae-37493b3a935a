
============
Introduction
============

.. include:: ../../README.rst
    :start-after:   :target: https://zenodo.org/badge/latestdoi/33720178
    :end-before: For information about individual releases,

Example
=======

To generate a recurrence network with 1000 nodes from a sinusoidal
signal and to compute its network transitivity, you can simply run:

.. literalinclude:: examples/modules/timeseries/recurrence_network.py