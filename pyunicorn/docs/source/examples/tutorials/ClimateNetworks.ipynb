{"cells": [{"cell_type": "markdown", "id": "c79b8e2b", "metadata": {}, "source": ["# Tutorial: Climate Networks"]}, {"cell_type": "markdown", "id": "677ae7d7", "metadata": {}, "source": ["The objective of this tutorial is to introduce climate networks, and to explain and illustrate their application with the `pyunicorn` package. First some theoretical background for understanding general climate networks will be given, and then some methods provided by `pyunicorn.climate.ClimateNetwork` will be illustrated. An introduction and application of coupled climate networks will follow. For a detailed discussion and further references, please consult __[<PERSON><PERSON> et al. (2015)](https://aip.scitation.org/doi/10.1063/1.4934554)__, on which this tutorial is based. "]}, {"cell_type": "markdown", "id": "76c98668", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "id": "a56c11e0", "metadata": {}, "source": ["_Climate networks (CN)_ are a way to apply complex network theory to the climate system, by assuming that each node represents a varying dynamical system. Of interest is then the collective behaviour of these interacting dynamical systems and the structure of the resulting network. This approach was first introduced by __[<PERSON><PERSON><PERSON> and <PERSON><PERSON> (2004)](https://www.sciencedirect.com/science/article/abs/pii/S0378437103009646)__.\n", "\n", "CN analysis is a versatile approach for investigating climatological data, and it can be used as a complementary method to classical techniques from multivariate statistics. The approach allows for the analysis of single fields of climatological time series, e.g., surface air temperature observed on a grid, or even two or more fields. It has been successfully applied in many cases, for example to dynamics and predictability of the El Niño Phenomenon (__[<PERSON><PERSON><PERSON> et al., 2013](https://arxiv.org/abs/1310.5494)__)."]}, {"cell_type": "markdown", "id": "05e76cc7", "metadata": {}, "source": ["## Theory of Climate Networks (CNs)"]}, {"cell_type": "markdown", "id": "fcc79d2d", "metadata": {}, "source": ["CNs are a typical application of _functional networks_, which allow to study the dynamical relationships between subsystems of a high-dimensional complex system by constructing networks from it. `pyunicorn` provides classes for the construction and analysis of such networks, representing the statistical interdependency structure within and between fields of time series using various similarity measures."]}, {"cell_type": "markdown", "id": "1860c9d0", "metadata": {}, "source": ["### Coupling Analysis"]}, {"cell_type": "markdown", "id": "30cd9555", "metadata": {}, "source": ["CNs represent strong statistical interrelationships between time series of climatological fields. These statistical interrelationships can be estimated with methods from the `funcnet.CouplingAnalysis` class in terms of matrices of _statistical similarities_ $\\textbf{S}$, such as the _(lagged) classical linear Pearson product-moment correlation coefficient_ (CC). The CC of two zero-mean time series variables $X,Y$, as implemented in `funcnet.CouplingAnalysis.cross_correlation()`, is given by \n", "$$\\rho_{XY}(\\tau)=\\frac{\\langle X_{t-\\tau}, Y_t \\rangle}{\\sigma_X \\sigma_Y}\\,,$$\n", "which depends on the covariance $\\langle X_{t-\\tau}, Y_t \\rangle$ and the standard deviations $\\sigma_X, \\sigma_Y$. Lags $\\tau > 0$ correspond to the linear association of past values of $X$ with $Y$, and vice versa for $\\tau < 0$. "]}, {"cell_type": "markdown", "id": "70377c40", "metadata": {}, "source": ["### Similarity Measures for CNs"]}, {"cell_type": "markdown", "id": "fadb2909", "metadata": {}, "source": ["By thresholding the matrix of a statistical similarity measure $\\textbf{S}$, the interrelationships between time series of climate networks can be reconstructed:\n", "$$A_{pq} = \\Theta(S_{pq}-\\beta)\\quad \\text{ if } p \\neq q; \\qquad 0\\quad\\text{otherwise}\\,,$$\n", "where $\\Theta$ is the Heaviside function, $\\beta$ denotes a threshold parameter, and $A_{pp} = 0$ for all nodes $p$ to exclude self-loops. A CN that is reconstructed using the Pearson CC from above is called a _Pearson correlation CN_."]}, {"cell_type": "markdown", "id": "9c64c013", "metadata": {}, "source": ["## Constructing CNs"]}, {"attachments": {}, "cell_type": "markdown", "id": "ff7f5d81-129e-4966-a7fc-a0d25aea87f3", "metadata": {}, "source": ["Having established some basic theoretic background, we will now use `pyunicorn` to construct a CN. We start by importing the required packages, by downloading an example __[NOAA dataset](https://psl.noaa.gov/repository/entry/show?entryid=synth%3Ae570c8f9-ec09-4e89-93b4-babd5651e7a9%3AL25jZXAucmVhbmFseXNpcy5kZXJpdmVkL3N1cmZhY2UvYWlyLm1vbi5tZWFuLm5j)__, and by specifying some metadata for it."]}, {"cell_type": "code", "execution_count": 1, "id": "e793f1a2", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from matplotlib import pyplot as plt\n", "from pyunicorn import climate"]}, {"cell_type": "code", "execution_count": 2, "id": "2a8e9a1d-982d-4216-8483-4f372f40d918", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["./data/air.mon.mean 100%[===================>]  27.46M  10.1MB/s    in 2.7s    \n", "2024-02-05 03:01:16 URL:https://psl.noaa.gov/repository/entry/get/air.mon.mean.nc?entryid=synth%3Ae570c8f9-ec09-4e89-93b4-babd5651e7a9%3AL25jZXAucmVhbmFseXNpcy5kZXJpdmVkL3N1cmZhY2UvYWlyLm1vbi5tZWFuLm5j [28791289/28791289] -> \"./data/air.mon.mean.nc\" [1]\n"]}], "source": ["DATA_NAME = \"air.mon.mean.nc\"\n", "DATA_URL = f\"https://psl.noaa.gov/repository/entry/get/{DATA_NAME}?entryid=synth%3Ae570c8f9-ec09-4e89-93b4-babd5651e7a9%3AL25jZXAucmVhbmFseXNpcy5kZXJpdmVkL3N1cmZhY2UvYWlyLm1vbi5tZWFuLm5j\"\n", "DATA_FILE = f\"./data/{DATA_NAME}\"\n", "![ -f {DATA_FILE} ] || wget -O {DATA_FILE} -nv --show-progress \"{DATA_URL}\""]}, {"cell_type": "code", "execution_count": 3, "id": "6f79efc6-63ca-4726-bb8f-003744170bcc", "metadata": {}, "outputs": [], "source": ["DATA_FILENAME = \"./data/air.mon.mean.nc\"\n", "#  Indicate data source (optional)\n", "DATA_SOURCE = \"ncep_ncar_reanalysis\"\n", "#  Type of data file (\"NetCDF\" indicates a NetCDF file with data on a regular\n", "#  lat-lon grid, \"iNetCDF\" allows for arbitrary grids - > see documentation).\n", "FILE_TYPE = \"NetCDF\"\n", "#  Name of observable in NetCDF file (\"air\" indicates surface air temperature\n", "#  in NCEP/NCAR reanalysis data)\n", "OBSERVABLE_NAME = \"air\"\n", "#  Select a region in time and space from the data (here the whole dataset)\n", "WINDOW = {\"time_min\": 0., \"time_max\": 0., \"lat_min\": 0, \"lon_min\": 0,\n", "          \"lat_max\": 30, \"lon_max\": 0}\n", "#  Indicate the length of the annual cycle in the data (e.g., 12 for monthly\n", "#  data). This is used for calculating climatological anomaly values.\n", "TIME_CYCLE = 12"]}, {"attachments": {}, "cell_type": "markdown", "id": "9f54ffe5-02a5-47e4-a459-870e1e8afef6", "metadata": {}, "source": ["Now we set some parameters for the CN construction, the first being the threshold $\\beta$ from above, and create a `ClimateData` object containing our data."]}, {"cell_type": "code", "execution_count": 4, "id": "baf245bd-2f3e-401d-bc1d-9943fee4bbf2", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading NetCDF File and converting data to NumPy array...\n", "Global attributes:\n", "description: Data from NCEP initialized reanalysis (4x/day).  These are the 0.9950 sigma level values\n", "platform: Model\n", "Conventions: COARDS\n", "NCO: 20121012\n", "history: Thu May  4 20:11:16 2000: ncrcat -d time,0,623 /Datasets/ncep.reanalysis.derived/surface/air.mon.mean.nc air.mon.mean.nc\n", "Thu May  4 18:11:50 2000: ncrcat -d time,0,622 /Datasets/ncep.reanalysis.derived/surface/air.mon.mean.nc ./surface/air.mon.mean.nc\n", "Mon Jul  5 23:47:18 1999: ncrcat ./air.mon.mean.nc /Datasets/ncep.reanalysis.derived/surface/air.mon.mean.nc /dm/dmwork/nmc.rean.ingest/combinedMMs/surface/air.mon.mean.nc\n", "/home/<USER>/crdc/cpreanjuke2farm/cpreanjuke2farm Mon Oct 23 21:04:20 1995 from air.sfc.gauss.85.nc\n", "created 95/03/13 by <PERSON><PERSON> (netCDF2.3)\n", "Converted to chunked, deflated non-packed NetCDF4 2014/09\n", "title: monthly mean air.sig995 from the NCEP Reanalysis\n", "dataset_title: NCEP-NCAR Reanalysis 1\n", "References: http://www.psl.noaa.gov/data/gridded/data.ncep.reanalysis.derived.html\n", "Variables (size):\n", "lat (73)\n", "lon (144)\n", "time (913)\n", "air (913)\n", "ClimateData:\n", "Data: 10512 grid points, 9597456 measurements.\n", "Geographical boundaries:\n", "         time     lat     lon\n", "   min 1297320.0  -90.00    0.00\n", "   max 1963536.0   90.00  357.50\n"]}], "source": ["#  For setting fixed threshold\n", "THRESHOLD = 0.5\n", "#  For setting fixed link density\n", "LINK_DENSITY = 0.005\n", "#  Indicates whether to use only data from winter months (DJF) for calculating\n", "#  correlations\n", "WINTER_ONLY = False\n", "\n", "data = climate.ClimateData.Load(\n", "    file_name=DATA_FILENAME, observable_name=OBSERVABLE_NAME,\n", "    data_source=DATA_SOURCE, file_type=FILE_TYPE,\n", "    window=WINDOW, time_cycle=TIME_CYCLE)\n", "print(data)"]}, {"attachments": {}, "cell_type": "markdown", "id": "2fade6f6-8457-436f-a52a-77464e92fd54", "metadata": {}, "source": ["Next, we construct a CN based on the Pearson CC, without lag and with fixed threshold. Alternatively, several other similarity measures and construction mechanisms may be used as well."]}, {"cell_type": "code", "execution_count": 5, "id": "c5326b90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating a Tsonis climate network...\n", "Calculating daily (monthly) anomaly values...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n"]}], "source": ["net = climate.TsonisClimateNetwork(\n", "    data, threshold=THRESHOLD, winter_only=WINTER_ONLY)"]}, {"cell_type": "code", "execution_count": 6, "id": "2cdee7ef-7d2f-46d9-83ed-c9253e0100c0", "metadata": {}, "outputs": [], "source": ["#  Create a climate network based on Pearson correlation without lag and with\n", "#  fixed link density\n", "# net = climate.TsonisClimateNetwork(\n", "#     data, link_density=LINK_DENSITY, winter_only=WINTER_ONLY)"]}, {"cell_type": "code", "execution_count": 7, "id": "e6bf3b44-193b-48c0-b7be-f056bd35d72c", "metadata": {}, "outputs": [], "source": ["#  Create a climate network based on <PERSON><PERSON><PERSON>'s rank order correlation without\n", "#  lag and with fixed threshold\n", "# net = climate.SpearmanClimateNetwork(\n", "#     data, threshold=THRESHOLD, winter_only=WINTER_ONLY)"]}, {"cell_type": "code", "execution_count": 8, "id": "5cdcfc8a-6448-4df3-b49f-f3e5d220f0f2", "metadata": {}, "outputs": [], "source": ["#  Create a climate network based on mutual information without lag and with\n", "#  fixed threshold\n", "# net = climate.MutualInfoClimateNetwork(\n", "#     data, threshold=THRESHOLD, winter_only=WINTER_ONLY)"]}, {"cell_type": "markdown", "id": "b443476e", "metadata": {}, "source": ["We finish by calculating some basic network measures for the resulting CN, optionally saving them to text files."]}, {"cell_type": "code", "execution_count": 9, "id": "4f568b0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Link density: 0.026892009342390742\n", "Calculating closeness...\n", "Calculating node betweenness...\n", "Calculating local clustering coefficients...\n", "Calculating average link distance...\n", "Calculating angular great circle distance...\n", "Calculating maximum link distance...\n"]}], "source": ["print(\"Link density:\", net.link_density)\n", "\n", "degree = net.degree()\n", "closeness = net.closeness()\n", "betweenness = net.betweenness()\n", "clustering = net.local_clustering()\n", "ald = net.average_link_distance()\n", "mld = net.max_link_distance()\n", "\n", "#  Save the grid (mainly vertex coordinates) to text files\n", "#data.grid.save_txt(filename=\"grid.txt\")\n", "#  Save the degree sequence. Other measures may be saved similarly.\n", "#np.savetxt(\"degree.txt\", degree)"]}, {"cell_type": "markdown", "id": "15af9941", "metadata": {}, "source": ["## Plotting CNs"]}, {"attachments": {}, "cell_type": "markdown", "id": "b26e5953-53c6-418a-b08b-509ad415081f", "metadata": {}, "source": ["`pyunicorn` provides a basic plotting feature based on the __[cartopy](https://scitools.org.uk/cartopy/docs/latest/)__ and `matplotlib` packages, which can be used to have a first look at the generated data. We start by initializing a `MapPlot` object:"]}, {"cell_type": "code", "execution_count": 10, "id": "b823297c", "metadata": {}, "outputs": [], "source": ["# create a Cartopy plot instance called map_plot\n", "# from the data with title DATA_SOURCE\n", "map_plot = climate.MapPlot(data.grid, DATA_SOURCE)"]}, {"cell_type": "markdown", "id": "422af668", "metadata": {}, "source": ["With `MapPlot.plot()`, we can now plot some of our previously calculated measures on the given grid."]}, {"cell_type": "code", "execution_count": 11, "id": "056f3a92", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot degree\n", "map_plot.plot(degree, \"Degree\")\n", "\n", "# add matplotlib.pyplot or cartopy commands to customize figure\n", "plt.set_cmap('plasma')\n", "# optionally save figure\n", "#plt.savefig('degree.png')"]}, {"cell_type": "markdown", "id": "b8feb1e0", "metadata": {}, "source": ["Try plotting more measures if you like."]}, {"cell_type": "code", "execution_count": 12, "id": "8b67424d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot betwenness\n", "map_plot.plot(np.log10(betweenness + 1), \"Betweenness (log10)\")\n", "\n", "# add matplotlib.pyplot or cartopy commands to customize figure\n", "plt.set_cmap('plasma')\n", "# optionally save figure\n", "#plt.savefig('degree.png')"]}], "metadata": {"kernelspec": {"display_name": "pyunicorn312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}