{"cells": [{"cell_type": "markdown", "id": "9b3f935f", "metadata": {}, "source": ["# Tutorial: Coupled Climate Networks"]}, {"cell_type": "markdown", "id": "41e36ae2", "metadata": {}, "source": ["The objective of this tutorial is to introduce *coupled climate subnetwork analysis*, which uses *interacting networks* in order to study the statistical relationships between several fields of climatological observables, or between a climatolical obervable at different vertical levels.\n", "\n", "First, some theoretical background on *interacting networks* is given and the method of *coupled climate network analysis* is explained. Then, some methods provided by ``pyunicorn`` are illustrated by the example of the Earth's atmosphere’s vertical dynamical structure. An introduction to (single layer) *climate networks* and their application with the ``pyunicorn`` package can be found in  the tutorial [Climate Networks](https://github.com/pik-copan/pyunicorn/blob/master/notebooks/tutorial_ClimateNetworks.ipynb). For a detailed discussion and further references, please consult [<PERSON><PERSON> et al. (2015)](https://aip.scitation.org/doi/10.1063/1.4934554) and [<PERSON><PERSON> et al. (2011)](https://link.springer.com/article/10.1140/epjb/e2011-10795-8), on which this tutorial is based."]}, {"cell_type": "markdown", "id": "122ca935", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "id": "********", "metadata": {}, "source": ["*Coupled climate networks* are a very useful tool for representing and studying the statistical relationship between different climatological variables, or between a single variable at different physically separable levels. This can be useful for finding spatial as well as temporal patterns that account for a large fraction of the fields' variance. The method can also be applied to study the complex interactions between different domains of the Earth system, e.g., the atmosphere, hydrosphere, cryosphere and biosphere, which still remains a great challenge for modern science. The urge to make progress in this field is particularly pressing, as substantial and mutually interacting components of the Earth system (tipping elements) may soon pass a bifurcation point (tipping point) due to global climate change. Mapping the complex interdependency structure of subsystems, components or processes of the Earth system to a network of interacting networks provides a natural, simplified and condensed mathematical representation."]}, {"cell_type": "markdown", "id": "5d0b07ef", "metadata": {}, "source": ["## Theory of Interacting Networks"]}, {"cell_type": "markdown", "id": "6fd973d9", "metadata": {}, "source": ["The structure of many complex systems can be described as a *network of interacting, or interdependent, networks*. Notable examples are representations of the mammalian cortex, systems of interacting populations of heterogeneous oscillators, or mutually interdependent infrastructure networks. \n", "\n", "``pyunicorn`` provides the class `core.InteractingNetworks`for constructing and analysing all kinds of interacting networks. *Coupled climate networks* are the application of *interacting networks* to *climate networks*, and hence, the class `climate.CoupledClimateNetworks` inherits from `core.InteractingNetworks` and `climate.ClimateNetworks`."]}, {"cell_type": "markdown", "id": "0eab5909", "metadata": {}, "source": ["*Interacting networks* can be represented by decomposing a network $G=(V,E)$ into a collection of $M$ *subnetworks* \n", "$G_i=(V_i,E_{ii})$. Here, $(V_i)_i$ denote the disjoint sets of nodes corresponding to each subnetwork, such that $\\bigcup^M_{i=1}V_i=V$. The internal link sets $(E_{ii})_i$ contain information on the connections within a subnetwork,\n", "and disjoint sets of cross-links $E_{ij}\\;(i\\neq j)$ connect nodes in different subnetworks, such that $\\bigcup^M_{i,j=1}E_{ij}=E$. \n", "Alternatively, a network of networks of this type can be represented by a standard adjacency matrix $A$ with   with block structure."]}, {"attachments": {"Systems-of-interacting-networks-or-networks-of-networks-are-a-natural-representation-of_W640.jpg": {"image/jpeg": "/9j/4AAQSkZJRgABAQIAOAA4AAD/2wBDAAUDBAQEAwUEBAQFBQUGBwwIBwcHBw8LCwkMEQ8SEhEPERETFhwXExQaFRERGCEYGh0dHx8fExciJCIeJBweHx7/2wBDAQUFBQcGBw4ICA4eFBEUHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh7/wAARCAHIAdQDASIAAhEBAxEB/8QAHQABAAEEAwEAAAAAAAAAAAAAAAcDBAYIAgUJAf/EAF4QAAEDBAAEBAIFBAoMCQoHAAEAAgMEBQYRBxIhMQgTQVEiYRQVMnGBI0JSkQkWGCQzYnJ1obEXNzhDVoKSlJWz0dMlNDZUV6XBw9ImRlNzdIWywsThNVVjg5Oj8f/EABQBAQAAAAAAAAAAAAAAAAAAAAD/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwDctERAREQEREBEVF84HRvU+6CsqbpmN9d/crdz3O7lcUFZ1QfzWgfeuBlkP5x/BcEQfS5x7uJ/FfERAX0OI7EhfEQcxLIPzj+K5tqHD7QBVFEF02Zh7nX3qoOvZWK+tc5v2SQgvUVGOcHo8a+YVYEEbB2EBERAREQEREBERAREQEREBERAREQEREBF8c4NGydBW8kzj0b0CCu57W9yAqbqhvo0lW6IKxqHegC+ee/2aqSIKwqD6tH4Lm2dh77CtkQXwII2CCismuLTtp0q8U4PR/Q+6CsiIgIiICIiAiIgIiICIiAiIgLjI9rB17+y4yyhnQdXK1JJOydlBzkkc/v29lwREBERAREQEREBERAREQEREBco3uYeh/BcUQXcUjXj2PsuasQSDsK5hl5vhd3/AK0FVERAREQEREBERAREQEREBERAXGR4Y3ZXIkAEnsFZyPL3bP4ID3l52VxREBF1V1yTHrTK6K63610EjQC5tTVxxEA9iQ4juq9lvFpvdF9Nst0obnSh7o/OpKhszOZp05vM0kbB7j0QXy0Y4veLDO4M9vVtwyotVPZaSqfT0kxpBJJK1nwl5LiRouBI6DoQp08TnHDGsNw7I8dtl7YcyNOKaGkZG/ngMrR+UJ1yjlY7nHXvy9Oq86EEqXbxE8ZrmXefndfED6U0MUGvu8tgW2Hg342u4gY+7FslrA/KLZHtssjviroB08z5vb0DvfofU68+12uI5DdsVyWgyGx1TqW4UMwlhkHuO4I9WkbBHqCQg9dUXWYnW19yxa1XG60IoK+qo4pqmlDubyJHMBczfyJI/Bdmgqwylvwu+z/UrkdeysVWppNHkPb0QXCIiAiIgIiICIiAiIgKnNJyDQ+1/UuUrwxu/X0VmSSST3KAep2UREBEUaeITi7aOEuItuVVD9NutYXR22hDuXzXgDmc4+jG7Gz36gDvsBJaLzZyHxOcZLtcXVUWUC2RcxLKaipYmxsHt8Qc4/4xKlPgF4tLz9e0th4nPp6qhqXtiZd44mxSU7j0Bla3TXM7bIAI6nqg3TRfAQRsHYK+oCIiAiIgIiIKVXU09JSy1VXPFT08TS+SWV4axjR3JJ6AfNRLdfEvwWt1w+hy5lHM4Eh8lNRTzRt/x2sIcP5O1r34++Jd0rc0HDagqZKe122KKavYx2vpM72h7Q73a1jmkDttxPoNasIPW7DcuxnMbX9Z4ve6K7UgPK59PJssPs5vdp+RAXeLyp4M8RL1w0zqiyK0zy+S2RrK6la7TaqDfxRuHbet6PodFehGXcScst94ZSYxwsvWUUM0EU8Fwgq4oYJGvZzdC/sRvWigk5F0WB3W/XrF6W45LjTsaucpd5tudWMqTEA4hp8xgAO26OtAjej2WF5ZVcfHZLWU+JWzh+yztl1S1N2mqTI5nLs8zYj0PNsdPbsglqCXm+F3f+tVVjuItyCPG6FuUyW+S9iL9+Ot4cKcyb/M5/i127rAbxgnGK8Xy4VMPHF9otclXJJS0FLjdM50EJJ5Gec4hziAQCTvet/cEwIrW0U9RSWmkpKusfW1EEDI5al7Q10z2tALyB0BJG9fNRHVeHew11TLNduIHEq5+bJzllVkLi0ezQGsHQeiCZXENaXOIAA2SewXVTZPjcNQ2nmyC0xzPc1rY31kYc4uOmgAnZJJAHur+4UlPX0FRQ1cfmU9TE6KVmyOZjgQRsdR0J7KOrPwC4P2msgq6DBLbHUU8rZYpHukkLHtIII5nHsQCgkG83Kgs9pqrrdKuKjoaSJ01RPK7TI2NGy4n2CwSzcdOE94v1FY7XmtBWXGulbDTQxMkd5j3HQbvl0Dv3KkGspqespJaSrgiqKeZhjlilYHMkaRotcD0II9CrK34/Ybe4Ot9kttI4EHcFKxh2Ox6BBRzTIqLFMYrchuMFZPS0bWukjpIDLKQ5wb8LB1PU7+7awjBONFuzDKaOx27Bc/pWVLXONyrrIYaOHTHPHmSF/w83LodDsuAUnogx7iDe75YMeNwx7FqnJ60StYKGCpZA4tO9u5n9Omh0+axrhvlvFC/wB/8nLOFTMRtH0YvbVOv0NZI6TY5W8kYBHQnex00pGRBinEt3EQWykbw3jxh1wdUaqXX4z+S2Hld9nyfi5ublHtra63hjScW4bhVS8R7rilVTOgAp4bLDM0sk31LnSDqNdFnqE6G0GCcV8bzLJDbYsU4hT4fFTmQ1ohtkVU6q3y8nxPI5OXTu298/XsFS4YYlkGKwVzL/nVzy2Spcx0b62Fsfka5thoaex2P1LM3HmcSfVfEGBcS+Fllz2501bdr1ktGKeIRCnt1ydTwyDmLtuaB1d17+2l2nDXA7HgFmqLVYpLhJDUVBqZXVlW+d7pCACdu7dGjt7LKUQYZlfCvh7lV++vcjxS3XS48jIzNUNLiWt3ygjeiOp9FkGM49Y8ZtTbVj1qo7XQte54p6WIRs5j3Oh6ldmsO4rcRse4dWBtxvUr5aqod5VBb6cc9TWy9AGRs7nqRs9hsfIEMX8TmJ8PrzwtvVRmM1vsx5WyQ3Z0I86OoaCI9aHNJv7PINktJA9CPNavoqugqPo9dSz003I1/lzRljuVzQ5rtHrotIIPqCCvRHDOHOR57kNLxA4yxsL4HeZZcWa7mpbcO4fMO0k3bex0119Gt7Dj9wBxvizX2+6VVdNZ7nSAxSVVPC17qiHuGOB11aeoPpsjrsaDzWUteEvAznnGm1U08RfbbW4XGuOuhZG4FrD/ACnljdexd7LZCfwY4LFYq5tNfsgqLq6mkFI+aaJsLJeU8hc1seyObWxzdv1rKfBTj+I0HC0XiyWg0F9nf9Bv3mSve8VNOS0t04kM3zc/KNfb+Q0E7oiICIiC7hfzsB9fVc1bUrtPLfdXKAiIgIiICIiAh6DaKjUv03lHcoKMr+dxPp6LiiICIiAvP79kDrqyo45QUc/MKektEDacb6EOdI5zv1kj/FW/00kcMT5ppGxxsaXPe46DQO5J9AtLvEVZ7l4g8invnC+wPuNBi9I+lmuzn+W25v5w7yacOHx+Xt7t7G+Y+7OYNSUVxcqGtttdLQ3GjqKOrhdyywVERjkYfYtcAQfvWX8PuFmdZtQ1t0x/Gq6vt9BE6aaUN5GyhveONxHxyEdmt2fkg3G4V5jx6qeGGMfVPDiy1tN9W07YbhWXtrXTxCMBsjmDq0uAB7lZM28+JSd0nJh2AUYAHKJ7jM/Z/wAT/wCyzfg3k+MZXw5tNxxF3JbIadlK2mcfylIY2hvkvHo5ugPmNEbBBOYIIYEfihqWRiSfhNQgkF5jZXPe0e2jtp/X+KGxeJGZ73SZ1g1KD9lkFrkeB/ldf61M6srvd7VZ6f6Rd7nRW+E7/KVU7Ym9O/VxAQRKMJ4/1DYmzcbLXResjqfF4JCOnYc50Rv7l9/sX8XpzI6t8QNze5/T9747TQADWuwcdH5jS7G/+IfhHaZvozcshutUTysgtcMlW559mmMFp/WuqHGnLr4w/tG4K5fcWv6RVF2Mdthd/GDnk7b/AF/JBzbwZzOQsFZx2zh7B9oQeXESde43/wBqf2BamcS/WHGjixN5nQtiv3lM1rRBbyELh5PiVyEAvq8HwqnPpFHJXVTfv5txn/7IOBl6vDg7OeMObXxvd1PRTNt1O8/ONnN0+4j70GnHiuwI4DxcqbdFcLjcqOqpoainqq+o8+ok+ANf5j9Dbg5p9Bppb8iYmXpFcfC/wfqseqbXHj89NUTjf1i2tlfVNf8Aphzy4b9xrR9lB108E2QMuDm2vN7XNRknT6mkkjkA9PhaXA/rHv8AJBq1ZLZXXq80dotlO+pra2dkFPEwdXvcdNH6yvW3Gbb9T43bLRziT6DRxU3MN/FyMDd9evooi4BeHHFeF9a2+VFXJfshDS1lZNEI46cEaPlR7OiR05iSddtbO5uQEREBco3FjthcUQXzSCAR2KKhSv7sP4KugIiICIiAiIgIiIC4TnUTv1LmqVV/B/igtkRaqfshHECS143acCttU6Opub/pld5biHCnYSGNPyc/Z/8A20Gyt2yXHLS4tut/tVAW9xU1kcWv8ohdfjfEHB8kvMlnx7LLLd6+OIzOhoqxkx5AQC74SQQC4frXk2u2xDI71iWR0eQ49XSUNyo388MzADrpogg9CCCQQehBQelfF/itSYdPT45YqB+RZrchy26zUx27Z7STH+9xjvs62N9gCR1/CjhVV0N9dxA4jV7Mgzmpb0kPWmtjDv8AI0zewAB0Xd++u5Lum8HlFh9wwE5ra6ya8ZPdHkX+41x5qoVA0XRE/msGwWgdxyk/KckBERAUIYSf2h+J7JcTd+TtOaUwvtuH5ratm21LB/GcNvPyDVN6hrxW0FXQ4vZuJNpi8y6YVco7hyjvLSuIZPHv2LeUn5NKCZUVraa+lulqpLnQyiakrIGTwSDs9j2hzT+IIV0gIiIPsZ08H5q9Vir4dQCgIiICIiAiIgKye7meXK5qHcsZ+fRWqAiKAPGVxlr+G2M0dkxuURZBeQ8tqOUH6JA3o54B6c7idN9tOPcBBN11vtktL2Mut4t9A94BY2pqWRF2zoa5iN9eirvuVvZa33V1dTC3siMzqoyt8oRgbLy7euUAb2vIe5V1bcq+avuNXPWVc7ueWeeQvkkd7ucepK7bGcouNpjFsmq6+px6eoiluNojrXww1rGPDix3Kem9a5tbCDeeuuF78Q92mtNiqK2zcK6V/JX3FrDHPfHg9Yot9WxDsT6+u+wnew2i2WGzUlms1FDQ2+kjEUEELdNY0eg/r33J6lQhimc8YMlxu3ScOeFeO49j0lMz6uqbtdWvi8rXwkQwac1oGun/APi7McP+N9/BOT8YoLNEe9Lj1qa3v7TP08a+4oLbjBDa6/xN8L6a7R0L6OhoLtV1IqYmuYWuiDG85d8Og4bG+x+9ZdkvGzhLi7TDcM3szXRN15FHJ9Jc3X5vLEHa+7ooUdwWxep8UFvxLI6zIcwpWYq+71Mt6uT5ZHSGpMTfibynlH6Pz676rYnF+HuDYx5Zx/EbJbpIztssFEwS79y/XMT8yUGsNw4iHHOJlZxG4QYZmFfj9bE6XKKSS0vgt8/KNiojfo8knUkkt9z6uUlYzmfHriNYaW94rYsHxqz18fm09VW3B9dLyH2EQ5eYeocBogggHopqul6sdC4wXO7W6lLhosqKhjN9O2nH2P8ASoEnlHA3JX5XjczbnwlvtUTcqejPnNstSXFpni5NjyS4crmjsRruGghkX9iTiTfhzZnxvyDkf/CU2P0sdva0foiQbLh8yFfWnw48KaWq+mXGyVWQVvQGpu9fLUuI+YLg0/5KyS5cX+F1umdDWZ9jkcjWB5aK+Nx0RsdAT3BB/FZZZLpQXq0Ut2tVVHV0NXE2annj+zIxw2HD5FBbY/jeO49EYrBYbXaWEcpbRUkcII9jygLtVEd58SPB+13SptU2Tzy19NUOppaeG2VTnNka4tcN+Xo6II6E/JSrb6uGvoKeupnF0FRE2WMkEEtcNjoe3QoK6KG/3QNuknENHww4qVr9bcIMbJLRvXUF4Kl24TSU1BUVENO+pliic9kLDp0hAJDR8zrX4oK6KIbRxR4jXG509OOB1+pKWSojikqaq5Qx+U0kBzy3WyGgk9O+lJ2SVF1pbBXVFjoIrhc44HOpaWSYRNmkA+Fpefsgn1QdgiinFL3x3q8ht0WQ4VitstD5B9NmiuTpZY2a68rQdF2+nqFnedR5TJitYzC57XBfiY/oslzDzTt/KN5+cMBd9jn1r116IO7RRpg9t42RZVT1OZZJiNRZGtcJqS20UrZHnldykOf207lPzG1lXEC05LebB9DxTLDi1x85r/p31fHWfAN7Z5cnTrsddgjSDIUUecOsLz6w319xyrirWZXTvie36G60Q0kYcSCHjkJI1ojXbqu54k4lV5faIKCjy2/4y6KbzHVFonEUsg5SOQkg9Ou+nqAgytpLXAj0V60ggEeqjfhrw4GF19TWuzjNslkqIREWX66/So4tHfMwco5SfvPRXvEjhljXEhttbkkl08u2ukMUdHWvp2v8zl3z8v2vsDXts+6DO1190vdltcrIrnd7fQyPbzNbUVLIy4e4DiNhY7ww4ZYlw5ZcW4tSVUBuT431Tp6uSdzywEN6vJ1oOPZcs44YYFm90gueWYzR3arp4fIiknLvhj5i7WgQO5J/FBkVlvNnvVO+os11oblDG/y3yUlQyVrXaB5SWkgHRHT5roMw4mYDiFebfk2W2m1VgiE30eonDZCwkgODe5B0f1LsMKw7F8LtsluxWyUdopJZPNkjpmcoe/QHMfUnQA2fZVbvimL3it+m3bG7NcKrlDfOqqGOV+h2HM5pOggYdlOP5fZW3nGbrT3S3ukdGJ4SeUub3HX2WEZzx74Y4bldTit7vdSL3Slnn0kFuqJXM5oxI3q1nKdtcD0J1vr6qR7dQUNuphTW+jpqOAEkRQRNjbs9zoDSuEHUYbkVtyzGqPIbOZ3UFY1zoTNC6J5AcW7LXdR1B7qPcl4401nyK42Sm4ZcTb1LQSuifUWywedTvcCRtr+cbaddDpSyiDr8aubrzj1vuz7fW219ZTRzuo6yIxz05c0ExyNPZzd6I9wopvXE7im2tqaW2cB7tWsiqnwwzy3yngZM0OLQ/wCJu2ggbGx6qQc/z3D8DtzK/LsgorTFJzeU2Z+5JuXW+Rg25+tjfKDrY911vDLiHbeI1rrrlZ7PfaKgp5WsgqbjRGBlY0jfPDs/E0dt9EGRUElRNQ081VTfRah8TXSwc4f5TyAS3mHQ6PTY7ry2495LkmU8Vr1c8rpoqS5slFO+lhk54oGxjlDWO2QW9ObYOjzE+q9BOIvCU55kr6m/5rkbcdMLWfUFDOKeB7hsOMjm/E8Hfbpr39FFfig8OVlreHVLcOHVmjorjj8Dg2jhLnGrptue5uyS50jSS4Ekk7cOpI0GiqIry1Wu5XWpbTWygqq2Zzg0Mgic92z2GgPkg23/AGOLH7s2TKModVTxWh7Y6FkA/g55gecvPzY0gD/1pW4618wvw8XnEsVtzMQ4n5NjN4FMx1dE1zKqgfU8oL3CBwA1zbHc9APnvt/rTxF4kP8AhGwYxxBomdTLbqg0FYW+pc145Cfk0FBNiKGaPxF4jRVUdDnVlyXBq1zuXlvFueIXH+LIzYI+ZA9fvUnYvlWNZRSmpxy/2y7xAbc6jqmS8v8AKDSSD8ig7hWl5t1Jd7PW2mviE1HW076eeM/nxvaWuH4glXahrxF8fbVwgrLZbn2SW93GvhfP5DKoQCKMHla5zi132jzAaH5p+WweFC4VdJiN44dXeXzLphVzktrnHvJTFxdTyfcW8wHyaFMq88qnxN3um4uXLiHjmL263T3O2soayjqJ31EczmO/JzOLQw87WhrfbQPv0nLwu+Jar4gZVU4tm8NsoLlUnzLVJSMdHFLofFCQ5zjzdNt69eo762GzaIiAr5vRoCsmDbgPcq9QEREBERAREQW9UfiDfYKiucx3K771wQFoj+yL0FZFxYsNzka76HUWRsELyOnPHPKXtH3CRh/xlvcsB458LrJxVwt9hur3U1TC4zUFaxu300uiN6/Oaezm+o9iAQHlmilPNOAfEPG+IFLhbKCnutxroZKih+h1DdVETCeZw5y0tIA3o/htS/wO8It9kvlNd+Jv0ait9O8SfVUMzZpagg7DZHN2xrD66JJ7dO6CVeEvDO/3zghgZOfZVizoLTt1Pa5xGJBJI6Rjn8wPUMc0aUs8NMNfhdnnoJcrybJXzzea6ovld9JkYeUDlYdDlZ03rr1J6rKI2MjjbHG1rGNADWtGgAOwAXJBrVknD+w8SPFlk9FfZa9tPbseo+lHVuhc5zn704t9Nfm/cVOmAYdZMHsH1HYGVLKLznTBtRUvmcHO1v4nknXTso64Tc1Z4l+MVwJf5cAtFHFsADpTOLx7/aG9/P8AVMyDA8q4O8NMpyOfIcgxKhuF0qA1ss8rn7eGtDW7Adro0AdvRZDacSxq1YiMSoLNRw2Hy5IvoHJzRFkjnOe0g72CXOJ+9d2iDV614rjfBHiH9R5VjlmueBZFUgWu9VlvilltlQegp6iVzd+WfzXOPQD25tbOUkFPS0sVPSQxQU8bQ2OOJoaxrR2AA6ALrcxxuz5bjVbj1/o46y31sZjljcO3s5p9HA6IPcEAqIuFuRXnhnmNPwhz6tkqqGfYxK+zfZqoh2pZXdhK0aA99gerdhOUcbI28sbGsbsnTRobJ2f6VyREBERAREQEREBERAREQFVpjqTXuFSX2M6eD80F6iIgIii3OePfDzGLm+yQV9Vkd+DzGLTYqc1lQXju08vwtI9QXA9+nQoJSVOoqKen8vz54ovNeI4+d4bzvPZo33J0eitJ/PumPSfQ5qm11FZSHyZXwjzaZz2fC4sd05mkg8p9RoqMsQ4C49b8io8qyy/X/Ncko5Wz09ddqxxZTyAggxRNIa0AjYB5gP1IM84hXq92DFai549jM+TXJjmMht0NQ2F0hc4N2Xu2ABvZOug2Vi3DFvGetyB944gy4zabS+ncyKyW5jppWPLmlr5JydcwAcNNJaQewPUSSiDrbvYLHd6yirLrZ6CvqKFznUklTTtkdA52uYsLgeUnQ6j2XZEbaR7oiCxI0SD6LqM0vQxzEbtfjTTVRt9HLUNgiYXvlc1pIYAOuyQB+K72pZp3MOxWPwZTYJ8yqcQhuUUl8paRlZPSNBLo4XO5Q4nWh1103vRadaI2EZ+GfhnZbTwisdZkGNWma/3CN1fWVE9DE6bmmcXtaSRsaY5o16aKmOGKKGJsUMbI42/ZaxoAH4Bc0QEREFGspqaspn01XTxVEEg0+KVgc1w+YPQqMsm8PvCy9VP02nx76huAO2VllmdRyMPuAz4N/e0qU0QQjLw94vYhC+fDeL/1pQwtLvoWWUwnaGt6/FUt+MDQ66A9T92iPF7ObxxDzyuye+Gl+kyhkTWUocIWMY3lAZzEnlOi7r124r1MyW001+x25WOsfNHTXClkpZXQvLHtY9paS0jsdFeW3GLh9eeGudVuMXhpd5Z8ykqQ3TamAk8kjfv1oj0II9EGHKtRVVTQ1sFbRzyU9TTyNlhljcWuje07a4EdiCAdqiudPDLUTxwQRullkcGMY0bLnE6AA9SSg9MvC7xOn4o8MIbvcKd0V1oZTRV7gzUc0rWtPmMPb4muaSPQkjtrcqrCOBOERcPuFVjxcNaKmCnEla5vXnqH/FId+oDiQPkAs3QVaZu5N+yuVTp2crOvcqogIiICIiAiIgsSdklERAREQQxxdP0DxI8HroDpk7rrQzbI681O3kHX+MT/AEBTOoX8Trvq678L8kedQ0GY0sM7t/Zjma5rnHp2HL/T+qaEBERBC3h01U8Q+MFzaWESZSaXYfzH8izl0f1/h1HoppUMeEs/SMcze7bLvrPNrnVB3LrYLmDp8vh/rUzoCIiAsV4p4HY+IeI1GO3yNwa8iSmqYwPOpZh9mWM+jh/SNj1WVIgh3grnd9pMgn4U8SpGty63R89DX6IjvVKO0zCe7wB8Q7nRPcO1MSwDjXw3pOIFghMFUbVkVrk+lWW7R9JKSdvUbOt8hIHMPkD3AWt198Y2Q2qyQ2eHGbXVZLRl9PcK59QZKKSRji3nibGQXNdrm3zADfTY6oN0EWjWJ+NHMae4MGUYzZbhQl3x/QRJTzNB9i572nXtodu/qtxOHeZWDPcUpclxus+k0NQCPiHK+J4+1G9v5rh6j7iNggkMhREQEREBEUEcbfE7hvDu7zWGho58kvVOeWohp5mxQwOGwWPlIdp49QGnXY6PRBO6LUjFfGxa6i4thybCKmgpHv19Ioq0TuYPnG5rd69SHfcFsnSZ5h1VhMWaR5HbmY9LGHtr5ZhHGOuuU82iHb6cp676a2gyRFHWAcYsVzvK5rJilNeblSwQvfJd20D2UIe0geX5jtHmOyR00eU6JXY8UsKuma01DQ0ebXvGaGNzzWstRayWra7Wm+aerNad22DzdR0CDPYKiCZ8kcU0b5IiBI1rgSwkbAI9OnVRvxJyPi0zJDjvDzB7dPGYWyOv13ruSkiLtjl8pn5RzgQd63rp06rvOFnDfE+HVsqaXGKKeF1a5stZPPUvmlqHjenPLievU9gAsxQdHgVJk9FitJT5jdaO63tvOamppKfyYnbeS0Nb7NaQ3fTet91dWjH7FZ6qsqrVZ7fQ1FdM6ermp6djH1EjiXOe9wG3Ekk7O12SICIiAiIgIis73dLdZbRVXe7VkNFQUkTpaieV3KyNgGySUFerEppZRTtjdNyHyxI4taXa6bIBIG/XRUW8CeH1zxChu97yyogrsyyGsdV3aqiO2NAJEcMZIH5Nrew0O+uwGrvgXmuU8QW3jKq2209txGonDMcjfG4Vc8Tdh08hJ0GuPVo1vv3GiZJljD2/P0KC0RfXAtOj3XxAREQEREBRT4meEtv4p4I+nD6ekvluDprbWynlaw6+KN59I3ADZ9CAeutGVlHHiNkyuThjU2TDLdU1d1vs8dqEsTSRRxTEtkneR9lobsc35pcD6INV8e8GmaXCliqqrLsYiglAcx9I+WpBadevI0H17Ejp3Uo8JvCLbcQzS05PeMuden22cVLKNtuEMZkaNsJcZHE8rtO7DelsLg+O0OJYfacZto/ettpI6aN2tF/KNFx+bjsn5kruUBVaePmdzHsFxijLz8vUq6aA0aHZB9REQEREBERAREQWKIRokIgIiIIh8YdDJVcAL7V07uWqtklNXwO39l0c7CT/AJJcpTstfFdLPRXOD+Cq6eOdnXfwvaHD+gqxzmyR5JhV7x6UAsuVvnpDv08xjmg/eNgrB/Cle33vgHjD5ifpVBTut1Qx32o3U7zEAfnytafxCCUV8cQ1pc4gADZJ9F9XT5xVfQcKvtbsD6PbqiXZGwOWNx7fggjHwXNMnAO23Mt065V1dVu28uJJqZG9d+vwKZlF3hPpfofh2w6Ll5eaidL9nl+3K9//AM3f17qUUBEXwkAbJ0Ag+ooxzTjtw8xys+q4LpLkV6cS2O12OI1k73fo/B8LT26FwPXsse+t+PuejVlsls4aWh/aruhFZcHN/SbCBysP8V4B6d0Hf+KvIp8Z4A5VcaSoENXJTNpISHaduaRsTuX5hr3Hp21v0XmKvRW5eGnHLzZbi/K8hv2V5HU0skVPdLpVvLaSRzSA6KJp5WgHR5TzdloNnOJ37C8mqsdyOgloq+mdpzXD4Xt2dPYfzmHXQjug6NbD+CzPs0xm7X2yYvhtTl8FXFHVTUkVc2nNNyO5TKC8EEnnAI6E6b16LXhb6eBHhZcMRxWtzK/U76a4X2ONtJA/7UdKPiDiPQvJB0ewa33KDL3cXOI8E3l1XATJmgt2DBcIZvX5DX9KM42ZPGyT6ZwN4hsewnpBSslBA9iCNn5DamdEELnxARwwskruEHFulBID3Pxz4Gk/MyDf6lyf4jcShkDKzFc9o9jbTPYJBv7tElTMiDXniJ4nMQgwO+mxMvtLfm0j2ULau1SxBsrhpry4jQAJDupG9Lz+mkkmlfNNI6SR7i573HZcT1JJ9SvWHipizM14c37FXyNjdcqJ8MUjuzJNbjcfkHhp/BeVWRWa549fa2yXmjlorhRTOhqIJB1Y4f1j1BHQggjoUFgtpPAHcoL1eMk4b3+jprrYaujbdG0dZG2WETRSxt3yO2CTzsJ6f3sLVtbofsenDy5W6K7cRLnTSU0NfTChtvOCDNFzh8smv0eZjAD66d+Iba0tPBS07KalgiggjHKyONga1o9gB0AVVEQXkf8ABt+4LkvjRpoHyX1AREQEREBEVs64UDbmy1urqZtfJEZ2Uplb5rowQC8M3stBIG9a2QgoZFerTjtkqr1fLhT2+3UjPMnqJ38rGD/tJPQAdSSAOpXV2+oxbiXw+gqjSx3bHr3TB4hq6cgSsJ2OZjhsEEbB9wCD2K6DPuE9pzrN7Ze8puVdcbNbItwY+8gUTqjZ/LvA6vOjrldsfgSDIMUbIomxRMayNjQ1rWjQaB2AHoEHGmghpqeOmpoY4YImBkccbQ1rGgaAAHQAD0VREQcZGNeOvf3VtJG5ncbHurtEFiiunQsd6aPyVN1O70IKCii5mGT9FPKk/RKDgsbwHNbLm1JcquxOqZKe33Ga3ySyRcrJJIiA50Z7PZ16OCvM9tV/uuFXe245XQW671VI+Gkqpt8sL3DXP02djZIPXrpW/CjBqHAuH1nxSiLSygpw2WRo15sp6ySH+U4k/jpBkCrRQE9X9B7KsxjWfZH4rkgAADQGgiIgIiICIiAiIgIiILOUakcPmuKq1I0/fuFSQEREBQn4fj+1rifxO4dSczI4LsL5b2nsYKtoc4N+THBo+8nv1U2KEuKjxh3iMwDONeXQX2KXGblIOgDnnzKbf3yb6+gagkjiZmtj4f4ZW5TkEzmUdKAAxg3JNIejY2D1cT+A6k6AJWiHE7xTcScvbX2+3zUmP2WsifA6kp4WSyOieC0tfK8E7IPdgYpV/ZJbjWsosKtLS5tDNJWVDxvo+Rgia3p7tEjv8taaINjuAPikv+FxWvGcppYLnjNKxlMySKIMqaWIdARrQkAHoRs6+177l5FxR4fY/j9LfbxltrpaGsp21NKTNuSeNw21zIxt7gQR2C8plvN4EcExCv4YMy24YzQVV8bcJoWVtSzznhjA3lLA7YYRsjbQD0QZqOMGcZp+S4T8N62opX9GXzISaOiH8ZrPtyt7fZIPyX1vBfKcw1Lxe4i3K8wO6ustn/eVAP4ruXT5R1PU8p+fvNyIMewrCMRwui+iYrj1vtMZGnup4QJJP5bz8T/8YlZCiIC6LMcOxbMKJlHlFgt13hYdxiqgDzGfdru7T9xC71EEa0XAbhFRUlXT0uCWlgqoXwvkewyyMa5paSxzy4sOj0LdELHfD1ebji99unBPKal0twsDBNY6uU9a+2OOoyPd0f2SB27D7JKmxRP4i8NutytdvzvDmcuZYnIaug5W9aqH+/UztdXBzd6HvsDXMUEsIsb4ZZlas9we2ZXZ3/vauhDnRl23QSDo+N38Zrtj59x0KyRAREQFgvE/hHgPEdrH5VYoqmriZyRVkT3RTsHtztI5h1PR2x17LOkQQli/hZ4QWO5Mr3WWruz43czI7jVGWIH5sADXD5OBCmqCKKCFkEEbIoo2hrGMaA1rQNAADsAuaIC+sG3Ae5XxVKYbl+7qgukREBEVjfrzabDbJbne7nR22hiG5KiqmbFG373OICC+VtdLhQWqglr7pXU1DRwjcs9TK2ONg9y5xAH4rqcDzLHM5sbr3i1ybcbe2okp/PbG5oL2HTtBwBI9j2IPRYBduCMGW5tVX/iRk9yyi3R1TpLVYnfkKGlj/ND2N/hXj9I6367QSTlkN7rMXuEOMXClobvLTuFDVVEXmxRyEfC4t9R+v30exwnhRwes2GXOXJrrcKvJ8yq2FtXfLg4ukIPdsTdkRM100Ouum9dFJFPDDT08dPTxMhhiaGRxsaGtY0DQAA6AAei5oCIiAiIgIiICIiAiL49vMxzdkbGtg6IQR1w14g3HNOI+aW2ioqT9q+PVEdvgr2ud5tRWAbnb3LS1nQeh7HrvpIyxzhvhViwHFYsbx6KZlFHLJMXTSeZJI+R5c5z3d3HrrZ9AB6LI0BERAREQEREBERAREQEREFKpbuPfsrZXxGwQfVWTgWuIPog+IiICj7xEYhLmvCC+WejD/rKKEVlvczfOKmE87A35u5S3/GUgog104hYz+6P8NVgvtpliGQwwiqgBcGtdUtBjngJ7NDnA6PTqGnoNrQ/ILLdsfu09pvduqbdX07i2WCojLHtP3H0+fYr0I4Yk8OePuScOJB5Vkybmv9g+HTWSnpUwt9tcvMB00G/NSPfbhg91zaDB73TW+4Xt9vNwhpKqh84fR+csLw5zS0fE3sSCfTaDzG4c4Lk2f5HFYsYtk1ZUPI8x4GooG/pyP7Nb9/fsNnQXp3wkwqi4e8O7RiNDJ5zKCEiSYt0ZpXOLpH69NucdD0Gh6KOeLOB1+GXiDirwqtkNPc7bF5d3slLGIobtRjqWhjRoStHUEDZ0O5ABlHh7l9jzrEqLJseqhPQ1bNgHXPE786N4BPK5p6Ef1jRQd+iIgIiICIiAiIg1/dPBwM41TfSpY6Ph5m87pRI9wZBa7nrbt+jWSAb9APkGFZTJ4juCrLj9AOd0hm3y8zaWoMX/APII+TXz2tSPGnxQueX8Tq/FIKmSOwY/Uupo6drtNlqWbbJK8erg7maPYA61zFQGg9fLHd7VfLZFc7LcqS40Uw3HUUszZI3fc5pIV8vNbwn8ULnw+4nW6jdVSOsF4qWUtwpiSWDnIa2YD0c0kHfct2PUL0pQEXGR7I43SSOaxjQS5zjoAD1KPkYyIyve1sbRzFxOgB779kHJFHtfxu4TUdXFSvz6xzzSvDGNpKj6TzOOtDcXMPVZrfq99rsdfc46GruD6SmknbS0rOeacsaXckbfVztaA9SQgvVcUrdNLvdQ/i/EPibkeSUVPBwduFnsjp2tq6+63GKKWOMnqWwfacQOvQn2UlZtZbjfcUq7RaMgq8erJgwR3GlY18kOngu0HdOoBb8t77hB3ixLijntvwCy09yrrRfLs6qqBTU1LaqI1E0kpaXBugQBsNPc+ix7h3wXsuKZPHlddkeUZPkLI3sbW3e5PlDA8acGsGmga6aO9enopOQYFwpyvOcqmuFZk+AS4ja+Vhtoqq1slVN1PMZIwPyfTl0D1HXv0VbN+FOD5rlVBkWV2j63qKCDyaenqJXGmA5i7mMW+Vx6n7QI1rp0WbogpUdLTUVLHS0dPDTU8TeWOKJgYxg9gB0AVVEQEREBERAREQEREBERAUTcTshv0/HTh5glguU1BFK6ou94fGR+VpoW6ZEQe7Xu2D+BHUdJZVg6y2d2QMyB1rojeGUxpG13kN88QF3N5fPrm5Obry71sn3KC/REQEREBERAREQEREBERAREQFQqWfnj7iq6EAgg9igsUX17SxxaV8QEREEWeJLDrnf8RpckxfmZluKVH1naHtBJkLeskGt9Q9rR09S0DsSuk4TTVef8XaPjLQxRMx6swxlrjAlaZI6r6V5ksTmg7BaQ4b1o9Pxm5axWPMrNwl8TGQYbTTTMxK91VPJVNc3UNqudSwvaGnehHK0de2unYN2Q2dUA5tb6zgdnNTxGx2klnwW8zD9tFrgbzfQpSdCtib6Df2gPf5jkn5Uqunp6ukmpKuGOennjdHLFI0Oa9jhotIPQggkaQUrTcKK62ymudtqYqqiqomzQTRnbZGOGw4H2IV0tfbDUVXADOYsZuU0knDG/1R+qK2VxIstU8kmne70icdkE6A2SezytgQQRsHYKD6iIgIiICIo340cTmYXHSWGw0JvuaXg+XaLTEdlxOx5sv6MTdEknW9HqAHOaHn/4kMerMb45ZdQVkbm+dc5qyAkk80MzzKw79fhcAfmCo9W/td4XbdlmKVNZneQ1tXntxlFTVXmJxcyF+tCFkR00xNHTXQ9OhaNARPL4KczFeWRZfj7qPfSV0czZNfyOUj3/ADkGv3C7GKzMeIdixmhbKZa+tjjc6M6Mce9ySb9OVgc7fyXq1YrdHaLJQ2qKepqI6Kmjp2TVMnmSyBjQ0Oe785x1sn1O1r5jmAY54ZcXZlrLLcstrJZmQXi6xMa11BSnfNJHF1IYCG7AJJ9XAALYGw3e2X6zUl4s1bDXW+rjEsFRC7bXtPqP9ncHoUEWVPhq4TVtbLV3az3G6SSyPlIqbrUcoc47Ogx7dKXIaeGKkZSsYPJZGIwx3xfCBrR336e6qogx6zYNhVmcHWfEMftzgdh1LbYYjvtvbWhZCi+taXOAHqgq0rNuLj2HZXC+MaGtDR6L6gIiICIiAiIgIiICIiAiIgIiICIiAoa8NlXUXbKOK94kqZZoP241FBAHP5mtbTtaPh6nQPPvp06qZVQoRRmEyUIgMUji8uh1yvd2J2O56IK6IiAiIgIiICIiAiIgIiICIiAiIg4TR87encdlaK+VGeLfxN7+qC3REQFED+Djb5lPFKoy99FV2jM/q9tLHTud51OKWFzA8ktAa8OII0XDp176UvrEeHedUeZV+UUtHRy0/wC168y2mV73hwmkj1zObrsNnWigwXgpmN6x/JJeDvEWp57/AEEfNZbnIeVt5oxsNcCe8rQNOGyeh7lpJmhYTxf4bWbiNYI6OukmobnRP8+1XSmJbPQzjRD2kEbGwNj116EAjEuFHE26Ul//ALGfFXybdmVPptHWfZpr1F2bLE7oOc66t6dewB21oSXmWNWbLsZrsdv9Gyst1bGY5Y3dx7OafRwOiCOoICibhBkt4wHL28Gs/rn1Lw0vxa9THQuNMP7w4n+/MGhrrsdP0S6cVhvGDh9auIuISWave+lrIXiottfF0lo6hv2JGnv36Eeo9jogMyRRTwL4g3S7VFdw/wA7jbR53YGhtW3WmV8PQMqovcOGidAaJ7DehKyAiKN+NHE5mFx0lhsNCb7ml4Pl2i0xHZcTsebL+jE3RJJ1vR6gBzmg40cTmYXHSWGw0JvuaXg+XaLTEdlxOx5sv6MTdEknW9HqAHObT4LcMX4pJWZVlVcL7nV4HNc7m8bEY6aghH5sbdAdNb0OgAADgtwxfiklZlWVVwvudXgc1zubxsRjpqCEfmxt0B01vQ6AAASYgIiIOE0cc0T4Zo2yRvaWvY4bDgehBHqFj2BYXjeBWertuN0rqGgnq5Kx8TpnOjjc/XMGBx0xo0NAaA0skXx7WvYWPaHNcNEEbBCCzsd2tl8tUF1s1fTXChnBMVRTyB7HgHR0R7EEfeFeqKMM4Z3fAuJ81ZhdwpqfBrsJJrnZJ+bVLU6+GSl10aHHW2nQAB7/AAhsroCuaePlbs9yuNPF+e78Aq6AiIgIiICIiAiIgIiICIiAiIgIiICIiC1uz5YrTWSQEiVkD3MIGzzBp1/Sov8AB5bqi1+G/EqKrilinbHUveyWMsc3nqpX6IPUfaUr1E0NPTyVFRKyGGJpfJI9wa1jQNkknoAB6qhabjb7tboLlaq6lr6Kobzw1NNK2WKRvu1zSQR9xQXSIiAiIgIiICIiAiIgIiICIiAiIgIiIKM0W9ub39QrdXypyxB/UdCgtVhHCLB6nCIMobVXOO4SX3I6u9FzITGI/P5PyetneuTv81nDmlp04aWD8EM6fxG4e02VvoI6EVFRPE2NkpkaWxyuYHAkDvy77IM3WLcTMAxniHjzrLktCJ42nnp6hh5Z6aT0fG/u09vkfUELKUQQFBlPEzgyz6HntHU5vhkOxFkdCwuraSMHQ+lRn7QA7vB+Zc49BL+F5ji2ZWxtyxa+0N2piASYJQXM36PYfiYfk4ArvSARojYKizMeA2DXq5OvdkjrMPyAdWXOwTGlfvv8TG/A4Egb6An3QWnicxenfip4kW25sseUYhE+tobjrpIxoJdTSa6uY/ZaB7u9iQdJOLPHbiHxDuEjq68z2y2EAR2y3yuigaP42juQ+u3E9+mh0WwviYxTjRYuC97huXEChyzGWGA1Qntwpq5jBMzl0WbDwHcuy49tn0WlSDKMP4hZtiVzZcceye6UMzSC4NqHOjk16PYSWvHycCt0/BHV2PLbRfc7r6ie555U1ZivFXVaL4oz1iZCB0ZEWjsPVhHZrQNBFtp+xuRVhybMJ2Nk+hCjp2SHfw+YXuLN/PQf/T7oN1kREBERARF9a0uOgNlB8UUcYbjxBwPJKfiJaXT5Bh8MAhvlgbG3zaaMEk1cBABcRv4gT2Ht1ZMEUIb1d1KqkAggjYKCzsdypbxZqK7UJkNLWwMqITJG6NxY9oc3bXAEHR7EK8WM8UMpq8NwqtySkx2uyA0XK+Wko3NEgi5vjkG+/K3btAEnXoNkXeDZVYs0xejyTG6+Ott1WzmZI3u0+rHDu1wPQg9kHdoiICIiAiIgIiICIiAiIgIiICIiAiIg6DiRFVT8OslgoYppquS01TII4mlz3vMLw0NA6kk60Auh8OtorLFwOxC1XGlqKSsgtkfnwVEZZJE8/EWuaeoIJ1orOKyogpKSarqZGxQQRukle7s1rRsk/cAVZ41fLTkljpr3Yq6Kvt1U0ugqIt8sgBLSRv5ghB2KIiAiIgIiICIiAiIgIiICIiAiIgIiICIiDjMwvicwHlcQQHa3o+6wzhFgMPDnh1bMOo7hJcYbeZuSeWMMc8STPl6gEjpz6+etru88vEmPYNf7/CITLbLZU1jPNBLNxxOeOYAg6+HrohWXCfIqzLeGmO5PcYYIKu6W+KqljhBDGl7QdN2SddfUoO5IIOiNIr1zQ4aIBVJ0DT9kkILdFUdC8em/uXAtcO4IQWF/tNBfbHXWW6U7aihroH09RE7s9jgQR+orz74x+GDP8QvFTNjVsqsnsTnl1PNRs8yoY30bJEPiLh220EHW+m9D0SUb8T8yvFn4l8PMRsckAlv9fO6tD4+c/RII+eTXX4Sd9D8ig0Kw7gJxaya5toqfCrtbWkgPqLrTvo4ox7kyAEj5NBPyW/XAHhbbOFGCMsNJMKyunk8+4VnJymeUgDoPRrQAAPvPclZ9VwmopJqcTSwmWNzPMiOns2NczT6Edwoh4GX7JrHk914SZvLW3G42qM1dpvMrXO+saBztNL3dfyjSeU79tfm7ITGi+gE9gSubYZD6a+9BTQAk6HVXDacfnHf3Kq1rW/ZACChHAT1d0Crsa1o00aX1EBERAWKYPw9xjC7xfrljlJLQm+VDKiqpmSn6OyRoILo4/ssJ2Sdd/uAAytEFnabrbLtDNNa7hS10cE76eV1PK2QRysOnsdo9HA9weoV4og/sX3nEOLVNl3DOopKGz3ioDcnskxLaZ7ev75hA+zKPYdCT7EqX0BERAREQEREBERAREQEREBERAREQWt4hfUWetp42875ad7Gt2OpLSAOqwHww2K9Y1wJxmxZFbZLbdKOKaOeme9rnM/LyFp20kdWlru/qpIXQ4Jl9izWxvvGPVMk9IyplpnmSF0bmyRu5XNLXAHoUHfIhIHcgLiZGfpBByRcPNj/SC+h7D2cP1oOSIiAiIgIiICIiAiIgIiICIiAiIg6vLrHS5Nil2xyulnhpLrRTUU74C0SNjlYWOLS4EA6cdbBX3ErJS41itpxyhkmkpLVQw0UD5iDI5kTAxpcQAC7TRvQA36BYl4istueDcF8iyqzTQw3CgiiNO+WMPaHPmYzqD33za/FZrZZJ5rNRTVLw+d9PG6VwbyguLRs69Ovogu0REBERB8LWnu0fqXQV2G4/W5vbsyqKNz7zbqWSlppvNcGsjk+0OTfLs++trIFE3hvyS85fHm+RXC4zVNskyeqprPC87ENLFytaW+o5iSdehCCV/LZ+iP1L6GMB2GtB+5fUQYzgWdY7mouzbFUyvltFdJQVsE8RilhlYddWO6gHR0SOuj6grJlHdVwz+j8baXiTj11+qn1NK+lv9GIudlyaABE7uOR7SB8XXoANd9yIgIiICIiAiIgIiICjHjfVcTLBJb8wwUw3e3Wtj/rfHXQjzK2IkEvik7iRoB037+jvsmTkQdJgWUWvNMPtuUWbz/oNwh82ITxGN7epBaQfUEEdNg66Ejqu7XUZjd5cdxK5XmktFXdpKCmdNHQ0YHmzco+w0e/6/kCeisOGed43xDxaHIsYrhU0r/hljcOWWnk0CY5G/muG/uPcEggkMmREQEREBERAREQEREBERAXwkAbJ0FTlmDejepVu5xcdk7QV3zj80b+ZUZcAcSveFWLJLVdmwsinyWurLf5UvOHUshaWE/onfN8Pp+KkVYvjWb2q/ZvlGIUtPWw3HGnUwrDOxojkE8Zex0ZDiSNDrsDuO6DKEREBFgPF/i5h/C36r/bXLWx/WfnfRvo9OZd+Vyc2+o1/CNWAfuueEH/Or1/o8/7UE/AkdiQubZnjv1+9a/fuueEH/Or1/o8/7VJXCXiXjHE6zVd2xaSrfTUlR9HlNRB5Z5+UO6DZ2NOCDPWTMd3+E/NVFYrnHI5nzHsgu0XGN7XjY/UuSAiIgIiICIiAiIgIiIOiz7ErJnGJVuLZHTvqbXW+X58bJXRl3JI2RvxNII+JjSu9UV+JbJr3jWN4x9QXOS3Vd0yqgtrpI42vc+OQv52AOBHUN/oUqICIiAiL4SB3ICD6uvx6x2fHrY22WK2UlsoWPe9tPSxCONrnuLnENHQbJJUU+L671cPCiDH7TUy09yyS9UNopZYXFsjXPlDyWkdd6jI/FS3TvZT08cEYcWxsDGlztnQGup9Sgu0Vv9Id+iE+kO9gguFDd1zbJcB46stOZVwq8KyxzI7FXGJrBbaxo0aWQgDYf3DnbO9eziJcFQfVoVKshoa4RCuooKgQytmiEsbX8kjfsvbvs4b6EdQgvUXRw5djcuYyYey705v0VIKx9FsiQQl3Lz+3fXTe+oXeICIiAiIgIiICIiAsRx/h1jFg4gXfNbPTz0VxvEDYq6GGYtppXNdvzTEOnmHsXff6ucTlyILelrqKrnqYKWsp55aSQRVDI5Q50Ly0O5XgH4TyuB0fQg+quFDmdcMsjtHEEcSOE1bS0N4rJY2X20VTi2iusfMNyOIB8uRoJPMBs9fUkPmNAREQEREBERAREQFbzTb21p6epX2ok7safvVBAREQFgNhwe4WzjlkmdCspjbr1bKamdTjm80TQ9A49OXXL077WfLDOI+dswy84lRVFsdU02Q3dlqNV5/IKaR4JjJHKebmII1sdkGZoiINOv2Sv/zA/wDeX/0q06W4v7JX/wCYH/vL/wClWnSAt6f2OT+1hkf89f8AcxrRZb0/scn9rDI/56/7mNBtCiIg+tJadg6KuYZA8aPRytUaSDsHqgvkXCJ4e3fqO65oCIiAiIgIiICIiDrMgx+zX/6v+ubfDW/VtdFcKPzN/kaiPfJINeo2f1rs1EXGi8XWl4zcIrJb7tXUUFyuVa+rgp5XMbUxwwNdyyAHTmgnsd99+ilxzg0bJ0EH1cHytb67PsFRkmc7o3oFSQVHzPPboFTJJ7lEQW0s1BLWNoJZaZ9S1onEDnNLwAej+XvrY6H3CuVDGFxTV/izza73CKSm+g2SlttqbUMLHVMPM2SeSIEfExsjmtLhsbeOvVTOgIiICIiCM+NvDu45RLZ8pxCsgteaWCoElvq5diOaInUkEpAJMbgT7+o/OKkqmfO2Fnmlol5RzhhPLzeut+i5KMeOGbZJw9qLHlEdHFW4bFOYMhZHCXVNOx+hHO071yNPca31A9egSqyf0ePxCrNc1w207XV0FXS19DBXUU8dRS1EbZYZY3czXscNhwPqCCrhpLTsHRQXqKjFMD0f0PuqyAiIgIiICIiAsB42O4i0dho73w5kpqmrtdR9IrbRNECbpBykOia/uxw7jXUnXto58iDFOFGe2biLiEWRWZlTC3zHU9VTVEZZLSzs1zxPB9RsdR06rK1bVT2UFvqaiGkkl8pj5jDTsBfK7q4ho6Aucfc9SVjHCXiLYOJGNG72V0sE8Ehgr7fUt5KiimHeORvoeh0ex/AgBmCIiAiIgLhM/kZ07nsuatJnc7yfQdAg4IiICLBeI/F7h5w8ulNbMwyD6sq6mHz4Y/oU83MzmLd7jY4DqD0J2sW/dP8AA7/Df/qqt/3SCY1hvF/A4uIOL09nfcXW2aluFPX01U2ESGKSJ2xppI7guG99N+qw790/wO/w3/6qrf8AdKR8Gy3H82xyHIsYuH0+2TueyOfyZIuYtcWu+F7WuGiD6IO8RRzwWze8ZPX5lY8lipIbzjl7ko3Npmlsb6Zw3BIAST8QDu/t+AkZBBXix4K33i9+1r6kuttt/wBU/SvN+mc/x+b5OuXlae3lne/cLWGDwxZZNxXqOHjL9ZjW09nbdZKr8r5IY6XyxH9nfN69taHdeiahfCA2r8XfEKpBaTb7HbqQ/FsgyDzNfLt2Qa/fuK87/wAK8b//ALv/AALYrwscKLxwmxC62a83Ggr5qyv+lMfSc/K1vltbo8wB3tpUvogIiICIiDlE8sdv9avAQRsdlYq4pXbBYfTsgrIiICIiAiIgIi4yPDG7Pf0CCzr7da6i4UVyq7dSVFdQF5oqiSFrpacvbyvMbiNs5m9DrWx0K+vcXnZKh2+1dTUeMSwW9tZMIafEZ6uSBpPI4uqDGC4dj669tfNTCgIiIMF4jcWsF4eXWjt2Y3Se1vrYnS00rqKaWKQNOnAOja4bGxsHsCD6hY/fPEZwkoMarrxSZbRXKSli546KAuE87uzWNa4A7J9ew7nor7xJcM4OJ/DKss8bGC70oNVapXdOWdoPwE+jXj4T7bB9AvMORjo5HMeNOaSCPmg2O4EcXL7lni6tWS5FUaF2EtuZAxx8umiew+VEwe3OGb9yST1K36Xj/aLhW2m60t0ttTJS1tJM2enmjOnRyNO2uHzBAW1nArxU8Qbhd6XHL7i0uZTS9Gy2yER1mvVxYB5bgPuZruSg3URU6aR0tPHK+GSB72BzopC0uYSPsnlJGx26Ej2JVRAREQFb3OhpLnbam3XCnjqaOqidDPDINtkY4EOaR6ggkK4RB09L+13FLZa7LDJQWij2yit1MZGxhztfDGwE/E7QPQbJ6ruFhvGPALdxGwiosNZI6lq2uFRbq5n8JR1Ld8krSOvQnR1rYJ7HqLjhPLmMmB29mfUcFNkMLXQ1RhmbI2bkJaJdt6AvADtDtv07AMqVWGUt6O6j+pUkQXwII2OoRWsEnIdH7JV0OvZAREQEREBERAWGQcNccpeKjuI1vFXQXeeldTVsVNNyU9bvXK+ZgHxvbrofuJ2QNZmiCm2op3VT6Vs8TqiNjXviDxzta4kNcR3AJa7R9dH2VRRDxi4cX5+TQcTuGNTHRZtRRiKoppX8tNeKcf3ibZADtAcrunYbI01zZYoH1MlDBJWwxwVTommaKOTnax5HxNDtDmAOxvQ37BBWREQcJncsZP4BWir1R7N/FUEBERBot+yNf2z8d/mX/v5Fq8tof2Rr+2fjv8y/9/ItXkBejXga/ucrL/7TV/69y85V6NeBr+5ysv8A7TV/69yCULZh9kt2b3fMaSKVl1vEEMFY7zTySNiGmHl7cwHTffSyBYlxitN/vXDK+0GLXSrtd8NMZKGopZCyTzWHmDAR2D+XkPycqPBTNY8/4Z2fJgAyqmh8uui1ryqlh5ZW69PiBI+RCDM1DHA0mr45cZ7o7mJkudBSbcR2gp3NHT8f6lM6hfwugVVdxRvQDdVec18UbhGW80cYYGnZ6n7R+47QTQiIgIi6/JboyyY5c71LE6aO30ktU6Np0XiNhcQD7nSDsEWqP7tnGv8AAi7/AOdx/wCxP3bONf4EXf8AzuP/AGINrlyidyvBWt/DjxY2DNM5tGK02JXOkmudQIGTSVLHNYSO5A6nstjkF8i4xHmjB+SIOSIiAiIg+OIa0k9grR7i9xJXOofzO5R2CpIKDfojq1xHkGqbGA4jXmBm96PrrarqHcFpqiXxWcRa+SKdsMNpttNE90Rax+2c7gHEacR07b7/ACUxICIiCIfFvxAGA8G7jLSz+XdrsDb6DlcA5rng88g/ks5jv3LfdeclJYb1VWauvNNaa6W2W8tbWVjIHGGnLnBrQ9+tNJJAAJ2dr0X8SGSSwiz4Vi1BSV2eX5zobXJJC17rdAf4aq2QSwAN6H3bvryaWI8esKtGB+F2iwO0tLqequtDTVEr38rqmV87XvkeR1JcWdvQAa+yEGjGJ21t5ym02hxcG11bDTEtOiA94b0J7d1vtj/h+yHh46ebhJxHqLUJ3B01JdrbBVMn16Oka1rgPuCxPxW8CqagNRxdwNkdBc7U9twuFC1uopfLPOZ2AfZeNczh2IBPQ75tnMYu9LkGN2y+0R3TXGkiqouu/hkaHD+goIk/bhx8xZusl4bWbLaZp0avHLgYpNe/kygucfkNK6tniN4ffSWUOUx37DK93T6NfbZJAdjv8TQ5uvmSFMStrnb6C50b6O5UVNW0z/tw1ETZGO+9rgQUFnjmR4/kdJ9Kx++W27Qa2ZKOqZMB9/KTr8V2qijIvDzwqutT9NpMfdj9eCSyrslQ+jezf6LWHkH+SurHDPi3jLd4Rxiq7hA37NDlFI2rBHpudvxj8AgmtFCn9kDjZjLNZfwkhvsDftVuL1/mb+6nfuQ/rCv7H4i+GNbVfQbvca/Frh60l+oX0r2/e7qwdfdyCXFjHFO85Fj+BXS94rZobzdKKLzmUcj3N8xjTt/LyjbncuyGjuf1HubLeLTeqMVlmulDcqY9pqSobKw/4zSQqzK2jfWPo2VcDqmP7cIkBe3oD1b3HQg/ig6fh3l9mzrDrflFhn82irY+YNP24njo6N49HNOwf6OmlkCwb6BgnCHHskyZrW2e21NQ641/5RxYZSANRx70C46Aa0DZIHtqDvDTxyvHErxD36K4udS2mqtTvqqg5ttgEUjSN+73Nc9zj8tdgNBtUq1NJr4D29FRRBfIuED+dnXuO65oCIiAiIgIiICxLi1DnUmGTS8Oq2hp7/Tysnjiq4Q+OrY07dBsnTC8dOb07bbvmGWogwbgzxIt3EbHZqqOkntd5t0v0W8WqpaRNRVA7tO9badHR9dHsQQM5VOGnp4ZJpIYIo3zv55XMYAZHaDeZxHc6AGz6Aeyw/hlxLx7PJrtRW5tXQXWz1T6autlfH5VTDpxDXlmz8LgNgj7u6DKqg7lPyCprnN/CuXBAREQay+LrgZmvFLNLRd8ZktTaakt30aT6XUOjdz+Y93QBp6acFC37jziz/6fG/8APn/7tegaIPPz9x5xZ/8AT43/AJ8//dqX+GPCbjJwhxCmueL3eju1Y18j7tjE05dS1TeY8rqeQgeXLyAA7ABIGyQOU7SIgwLhZxWxrPfOoKf6Racgo9trrJcGeVV07h3+E/ab1+0Pcb0eiyqWssNhfR2+Sot1sdXTujpIC5kRnlO3EMb05nHqTrqsR4rcJcaz50NxlNRZsjpOtDfLc7yquBw7bcPtt/in3OiN7WrPixv/ABax/HsexrNo6Sokt9x+l23JqFjR9MMbfhLgRuKZuwTy6Dt+utkN51C/g3/LcJKu6FwcblfrhVlweXk7mLdk+p+H/tWoH7p/jj/hv/1VRf7pdNhPHbiphmM0uN41lP0C1UnP5EH1fTScvO9z3fE+MuO3OJ6k9/ZB6gItfPBNxGzLiJjORVmZXj6zno6yKKB30aGHkaWEkaja0Hr7rYJxDWlziAANkn0QfVjfFT+1hlX8y1n+pesazHjnwxxmc0dRk0FyuJdyMoLU01k7n/o6j2Af5RCwDO+IPFnMMGv37WeGDsesLrZUGouOSz+VMYfLdzclO34g7l3okkbI/EPP1ERBJHhf/ugcM/nNn9RXqEvL3wv/AN0Dhn85s/qK9QkFzTH8no+6KlEdNPX1RBdIiIC4yu5GE+vouStql238vsgpIiILK33a1XGqrKW33Oiq6ihk8qrigna99O/W+WQAksdr0Oir1RRwHsl2t2YcT7ldaCspBcsmkkpHTwljZoGtAa9mwOZp6jfrpSugLoOIeW2jB8NuOUXyYR0dDEXluwHSu7Njbvu5x0B9679a/wBFvjrxd+nuLpOHOF1eqdhH5O7XNu9yezo499PfY7h50Hf+HvELtLU3DivnEHLluTNDo6dwP/BlF0MVO0HqDoNLvXsD1Dt0vFPuqj4bWdvN+/s6tok0B/Bt8wu7+o6H8CpmUL8etVXGLg3a9gmS91VWGnZ2IIQ7evlzd/RBL13oKa6WmstdazzKWsgfBM39Jj2lrh+olRP4RK+oHC2fEri/muOJXWqs1RvoSI5C5jte3K4NH8lTEoUxLeJ+LLK7F1ZQ5faILzTju0VEJ8qRo+ZG3n8PkEE1oiICIiArC+WWz3yjNFe7VQXOlPeGsp2TMP8AiuBCv0QRDefDnwzqap1dY6O54ncT2q7DcJKZ49Rpuywfg1QH4o/D5ldusFRnjM0rcsFsha2obXU4FXHTtJ+MytP5Xl3s7AIGzvotuM1znEMMo/pWU5FbrUzW2tnmAkf/ACWD4ndj2BWvHHXxMxP4e3CPDMXvMlDc430MF9r4Po9MXPYdmFrhuUhvX01sE+xDSypvF3qaH6DU3SumpOYP8iSoe6PmG9HlJ1vqevzK7zhLm9fw64gWzL7bTxVM9CX7glcWslY9jmOaSPk469iAfRYqu8wHHKnL81tGMUk8dPPc6tlMyWQEtYXHXMQOuh3Qb68LvFRw3y8w0d3mlxa5yENENceaBzv4swHLr5vDFPAII2DsFRBwV8POCcNmQ1zaUXu/NAJuNbGCWO94mdRH9/V3zUwIOcL+R+/Q9CrtWKu4Hc0Y9x0Qc0REBERAREQEREBYRkXDKw3biTZeIEMtXa77bCWTT0Twz6dAWkeTONfG0HR99DXtrN0QWFRNC2t+jmWMTPaXtjLhzOaNAkDvoFzdn5j3X1Rtx04b3DJK2hzHDbkbPnFkYfq6qLvyVRHsl1NMOxY7r92/YlZlhdZe6/FLbW5Jao7TeJqdrqyjjmErYZPUBw6H37nW9bOtkO3REQEREBERAWqP7JB/yMxP+cJv9WFtHeLnbrPbJ7ndq6moaKnYXzVFRIGRxt9y49AtHPGhxPo+Itms37XLPcDjlDXTMjvVRGY4q2bl0WwtPUtAGy4+4Gh6hrGiIg2f8F0/FV+N5BbeHNDj0UE1bE6sul2kcRAeQ6ayJnVztddkEdgp+PAi4ZOWzcVeJGRZUD1fbqV4oKA/IxR9TrtvYKj79je/5GZb/OEP+rK2uQY3hWB4bhlP5GLY3bbUNcrpIIB5rx/GkPxO/Eldnk9rbe8aullfMYG3CjmpTIG8xYJGFvNr11vel2KINR/3Edq/6Qa3/Rjf94n7iO1f9INb/oxv+8W3CINaeGnhLt2F55Z8qizWrrX2ypE7YHW9rBJr0Luc6/UtlkRBVhaS0/eiqUw/JfeUQVUREAnQ2rInZJPqrqc6id+pWiAiIg6jFMlseU2+W4WCvbXUsNRJTPkaxzQJWHTm/EBvR9ey7dRh4ZcXvmJ8NJLdkdG+juc11rKmaJz2O1zzHlILCRotAPf1WQ8Xc7t3DzB6vIq5hnmBENDRtPx1dS7YjiaB1JJHXQOgCfRBg/H/ACa7Xm7UPBzCKry8hv8AGX3OrYf/AMLt3aSUkdnOHwtHfr6FzSpNwjGbTh2J27GbHT+Rb7fCIom+rvUucfVziS4n1JKwfw+4HcsdtVdlmYPbVZtksgq7rMW9adpHwUzfZrB00Om+nUNClFAUMcQSazxY8MKY82rfbLrVjqANyRCL8e39XzUzqGJ/3941KZgALLZgrpC7yydSSVnLy77D4Tsa+Y+4JnUJ+JgjG8l4dcTGkRssd9FFXyb1y0dW3y5HH3A0P8r07rBvE1xh4tcIc9jFFHZrljdzj823urKFx8twGpIXOY9pJaSHA+zm99FQTxk8SuYcSsJOKV1stdso5pWyVZpA8mcMIc1vxk8oDgHdOpIHpvYbp8HOLtk4nX3K6OwMJorFUQwxVJPWqa8P/KAejSWO16kaJ1vSkheefgcz+yYRxMuMOSXWC2Wy62/yvPncWxidkjSzmPZo5TJ1PTr3W2mUeIHh3a636rslbVZheXj8lb8egNY957D42/B30PtE/JBLCsb5eLTY7e+4Xq50dto4xt89VO2Jg/FxA9FDxr/EFnfS3W2zcM7TIek9cRXXEtPqIx8DT36O0e3VX1j8PeIG4MvGcXC757dwebz73UmSFp9mQj4Q3QHwu5ggo3HxB2S41kls4a43fc/uLDyuNup3R0kZ/jzvGmjqOuiOvdW/7W+PecHmyTLbbw+tbx1oLCz6RWkezp3HTD82E/cpnttDRW2ijobdR09HSxDUcFPEI42D2DWgAK4QRlhXAnhtjNX9Y/Upvl2J2+43qQ1k7nfpfH8LT82tBXYcUeFmE59V0NxzWCpraS0xSeTTGrfDTs3ove4MIJOmjudab277z1Rn4n8ilxvgfkU9JzGvroRbaNjPtOlqHeV8PzAc534II58LPDbC8j4Y5Bfbnidufb8outU+jp5acbgoWP5Io2OPxN0WuPMDvej36rB8c8PF44deKDEa23Ce54pNWyywVhHx0zmQyPEc2ux+Ho7oHfI9FtVw3x2LEsAsWMxAattBDTuIO+Z7WgPd+Ltn8VkCAiIgKtSu04t91RXKI6kafmgvEREBERAREQEREBERBQqh0DvwWPZxDkdRiNzhxGso6K/OgP0Gari8yJsnpzD59RvrokHR1o5NI3mjIVmgjngrxLGa09bZb7bnWPMrKRFeLVJ05HeksZ/Oid3B2dbHUghzpGVGOkpY6yWsZTQtqZmNZLM1gD3tbvlBd3IHM7QPbZ91pHxn8TPE/GuK2S4/Zqq3xW+3V8lPAyegaXhrTrqT1O9bB9RpBvEi87/3W3GL/n9o/wBHM/2p+624xf8AP7R/o5n+1B6IKM+KHGOxYhcmY3aaOqynMJx+97HbQXy/fK4AiJuup31111rqsBx7KeLfGixW+nxyGXCcakpo23DIp49Vdc/lHmfRI/zGl3Np/wCIIILVLPC/hpiXDu2vpset/wC+putXcKg+ZV1bidl0khGzs9dDQ32CCP7RwmyXPbnBkfHC5RVzIn+bRYtQvLbfSH080jrM8fMkdxtwOlK15xDFLzb6W3XjGLJcaKj/AOLU9XQRSxwdNfA1zSG9OnRd2iDDv7FPC/8A6N8O/wBB03/gXVZfwR4ZX/GLjZo8Kxy1S1cDo462itUEU9O8/ZexzWg7B0db69j0JUjIghLwo1tFbrNd+H9wslqsuX43O2nuraOljg+nx6/I1Xwgc/MzW3HrvROuYKbVCniGstyxm9WzjXilM6W5WBhhvlLENGvthO5Afd0euYH07/mgKWsavVtyLH6C+2epbU2+vgbPTyt/Oa4bG/YjsR3B2Cg7FERAREQERc4W80gHp3KC5jHKwD5IuSICIiCjVH4APmrdV6v838VQQFb3GrgoLfU19U4tgponTSuA3prQSTod+gKwDO+N3DvBsqdjeXXWptNb5TJo3SUM0kcrHdnNdG13TYc3rrRafxwrjB4iuGcHCy+Px/Jaa8XKrpJKSkpacOD/ADJWOaHuDgNNb9ok+2u5GwljDs4xnKrDFerTc4jTSUja0if8k+OBzntEj2u6taTG/RPT4Som4ewzcaeKR4l3OF4wvHJn0+K00g0KuoB1JWuB7gEab7EDsWnepXhxp8kzDNpOHlFepKK35HTsprrNzbkFHB+ULI9noS1pZrRGnHY1tektitVvsdmo7PaqWOkoKKFsFPCwdGMaNAIL1Fa3S4UFron1tzrqahpY/tzVErY2N+9ziAFFd88RHD+Cuda8YN1zW7A6FHYKJ9Ts+nx9GEfMEoJeUKYlLFJ4t+INdNMxkVtx+gpnvfLoRh/5U/IDpv8Ap9V8+uvEJmOxZ8Zx/h9b3npU3ao+mVnL7tjYOVp/ivH4+8ccKOD9szjipxHPEe93TKprZcKSmml8w0cNbI2LqZI4z+boNaAdAfeg7fxkcReEt64W3TGJL9RXe/xvZLb4qB3nOhnDtcxkaCxoDS4OBOyCQOvbRFb0cdfDpeMyvdrsmDW3GMTxW2Q875A3kkqp3/acWxsJfytDQC8g9Xe6jbi/4Vabh/wgu2XftuqLpcrd5UhhbRthhcx0jWOHV5OwHc299eXWuvQNabVb6663Gnttso6itral4jgp4Iy+SRx7BrR1JW6/hS4A8QMNrosjyHKq3H45C18lkoZWyeeB2E5O2D16NBOj9ppUA+GPhzS5XxtGI5bQVkUMNHPLPG1zopYnNaOR7XDqNOc1wPUHp3BW2Hl8Y+EQHkmfihh0Q+w74bxRsA9D2nA/Fx/ihBPCLDuGnEzDuIVG+XG7qySph/4zQTjyqqmIOiJIj1HXpvqPmsxQEREBQnxs/wDKjjhwzwEAPpqaqkyO4sPbkpwRBseoL+Zv+1TYoT4P6yrxBcSs5dp9Na3w4zb3d9CIB9QN/wDrOU9Pf8SE2IiICIiAiIgvh1G0XyP+Db9wX1AREQEREBERAREQFazs5XkjsVdLjIznYR6+iCzXl74oP7oHNP5zf/UF6hEEHR7heXvig/ugc0/nN/8AUEEboiIPWbhV04X4qB/+S0f+pYskWN8K/wC1hiv8y0f+pYskQEREBERBwmjjmifDNG2SN7S17HDYcD0II9QoI4XyycIuLVVwor5HNxa/vkuGJTPPwwvJLpqPZ9idtH3er1Zfsgf9oaL+eqf/AOCVefiD2KReOq2r/Y3/APl1lX82Rf61Bu+iIgK5pmcrOY9yqMLOd3yHdXaAiIgIiIKFV3aqCuKodGlW6CDPGbwwizvhhPeaKONt8x+N9XTvJDfMgA3LESfTlHMPm0D1K86F6AePDPnY5wxhxC3yH6zyWQwuDPtNpWEGT8XEsZr1Dney0oj4c56+wVN/GHX5tqpY/Nmq30MjY2s9XbI6gepG9DqUHU4tf7xi9/pb9j9wmt9zpHF0FRFrmYS0tPfoQQSCD0IJWx/D7jzx34lXKhxO0PMbttbWXO0WaOapawnXO/zXthZ69fg+XXS174eY7cMrzW149a6WCrrKybljgmkdGyTQLi1zm9WggHqO3yW9fD7ijivDWhpsRzLh5WcL+VwYyUQme31D9a5hUsB5nHXUu3rptx7oO+tnh3w6atZcs3ud/wA6uTSXCW9V73xNPs2JpDQ35HY/oUqWKy2ew0DaCx2qhtdI3tBR07YWD/FaAFzs11tl5t8dxs9xpLjRyjcdRSzNljcPk5pIKvEBQv4YeWru/FW8Asd9Izmup2uB3zMiDA079tO/rU0KGfB4Hy8Lrlcn8xNxyK41W3a2dy8u9Dt9lBMywXxB283TgbmtI1pc76lqZWgb2XRxl4HT5tCzpdfklALrjtytZAIrKSWn0f47C31+9BhPByy4/d8UwviB9WwC/PxmmpnVjW8r3MdFGXMd25tOZ0J7ddd1Iyibwf130/w54o9x/KQQzUz2nu0xzyMAP4AH8VLKCN+JnBrEs1rmXtgqsfyaA81Pe7TJ5FSxw3rmI6PHX1666AhYh+3vibwp1BxQtLsrxqM8rcos0GpYWdAHVNOO3zc3p/KKndfCARojYKDp8OyrHcvssd4xm8Ul1oZO0sD98p/Rc3u138VwBHsu5UQZnwOt7rxLlXDa7z4JlDvidNQN/elUep1NB9lwJ7kD5kOXXW3jJf8ACq+Kxcb8d+pHPd5dPkdva6a2VR9ObW3ROOux+8hoQStnl/hxbCb1klRymO2UM1UWn84sYSG/iQB+KwfwqWGax8DbC+t5jcLsx91rHuADnyVDvMBPz5Cwfgui8Ul2gyLh7juH2Kviqhm96paCOemlD2mmDw+SRrhsFo5W7I30JU1UlPDS0sVLTRtighYI42NGg1oGgB8gAgqoiICIiAiIgvI/4Nv3Bcl8YNNA+S+oCIiAiIgIiICIiAiIgpTx8w5m9/61GGRcDeFWQ3yrvd5w+krLhWSGWondPMC9x9SA8D9SlRUZoubbm9/b3QRF+504L/4CUX+cT/8AjT9zpwX/AMBKL/OJ/wDxqVeyILe20VLbrdTW6iiENLSwshhjBJDGNAa0devQAK4REBERAREQa8fsgf8AaGi/nqn/APglXn4vRTxx2K95DwWit9gs9xu9YLvBIaehpnzycoZLt3KwE6Gx1+YWjn9inih/0b5j/oOp/wDAgw5bV/sb/wDy6yr+bIv9aoF/sU8UP+jfMf8AQdT/AOBbLeAPDstxrMslnyPFr5ZoZrfGyKSvt8tO17hJshpe0AnXoEG4a+saXu0F9YxzzoBXUbAxuh+JQI2hjeULkiICIiAiIgp1I3Fv2KtVevHMwj3CskFq+30D7iy4voaZ1axnltqDE0ytbsnlDtbA2T0+ZVy9rXsLHtDmuGiCNghfUQQLSeHa0Y94g7DxGxPyKO1RSTvr7Xy6ZE91PI1r4fZvO5u2enp06CcrjQ0VxopaK4UlPWUso5ZIZ4xIx49i07BCuEQQ1eOAFmobjJeuGWQXXh/d3nbvq5/mUUp//Up3nlI7dAQPkrMZ5xgwDbOIeENyu0R973jA5pWt39qSmOj26kt5WjXqpxRBgOLcXcDy2wVtxxnI6KqnpaaSaSkl3HUR8jS480LtP0NdwNdDorofBxRCi8N2Jx8unSR1EzjycpPPUSuG/foQN+wC4eI/htgV44fZLlV0x+lZeLba6msguFMfo9R5kcT3N29uufZGtO2OqwPg/Z+OGF8LcYumKV9nzKxVdrp6wWKuApammbJGJCyGcdHfa/P7b0B6oNm0UOWbxDYkJpLXmNtu+F36KNz/AKuu8HlCYj0imOmO300XFu9/irG2eKzg1Vu5aq93C2u7EVVtmJB9j5Yd9yCv4SZI7fgmV2eeZsbbFllzo3l7tCNrXh5PU6A+In27qX7PcqC8Wqlulrqoquhq4mywTxO2yRjhsOB9ivOzi5xaqIbln+MYTdGSWDJr464T1sPM100b2N3G3YBDS7fN76A7E72y8EV9F68PNmhLuaW1zz0Mh3+i8vaP8iRiCbEREBW9yoaK5UE1BcaSCspJ28ksE8YfHI32c09CFcL49zWML3uDWtGySdABBqDceGtdb/E3VUvBg0dufidtbc3UNylfNRCrqNtdExvUxeZEQd77tGtDREwYdxxt/wBcxYrxLtE+CZO7oyOvP7zq9dOaGf7JBPoT7AFxVp4UmPvduzDiTOHF+W36eamc4Hf0OAmKEde+tPClHMcVx3L7LJZ8ms9JdaGTvFOzfKf0mu7td/GaQR7oOyrKykoqb6TWVUFNBsDzJZAxuydDqenUqpDLFNE2WGRkkbvsuY4EH8QtdMy4Q5ziuK3KxYTXuzLCq2F0VRit4qNTQMOz+9Kg/ZLTotDugIB+IrRmtZfMXvdVQP8ArSz1sEnJLFIH08zO+g5vQg6P9KDcTxm8fpbIZuHmE1/JczoXauhd1p29/IYf0z05iPsj4e5PLs3h14ZkOI2e/wAQaGXKhgq2hp6ASMD9D9a8jJZHyyOlle573kuc5x2XE9yT6lbVeHvxWUWJYrasOzCxVElDb4hTw3GifzvDATrnjcRsAEDbXdh2QbvL6wbcB7lY5gGcYrndmN2xO9U10pWkNkMew+JxG+V7Hac069CAsmphuT7uqC6REQEREBERAREQEREBERAREQcJI2v+R91bPjczuOnurxEFiiunwsd26H5Kk6B47aKCki+ljh3af1L4gIiAE9ggIuTY3ns0/iqjac/nHX3IKKqxwud1d0CrsjazsPxXJB8a0NGmjQX1EQEREBERAREQFaTN5ZCPxCu1SqW7bzD0QWyIiAiIgIiII18UlUaPw+ZnMDrmtzou2/tuDP8A5ll3D2lNDgOPUTmlpp7XTREHuOWJo/7FG3jQkcfD3eaCNzWyXCqoqVhJ11NTG78ejT09tqYoY2QxMiiaGsY0Na0egHYIOuyfHbDk1sfbMhtFDdaN/eGqhbI0H3G+x+Y6rQXxlcHbTwzye23LGW+RY7wx4jpHzF7qeaPXOG8xLiwhzSCSdHYJ7L0OXnF4vMyrOIXGGsdbIqips1okFpoHRxuLJJQTzka6Fzn8wGu7WtQQks24X8Vs64cTudil9mpaaSTzJqOQCSnldoDZY7oCQAOYaOgOvQKwznh9mmEMo35XjldaWVreandOwcr/AHGwSA7+KdH5LY/9j/4e4vkFLfMrv1lguVZb6yKChdUt544Ty8znBp+Eu6t6kEjprW0E/eHTiNmnEPHTccqwSWwR8gMFc2Tlhq/myJ/5Ro+fxA+hUrIiAo38TORvxjgdk1dTucKyopDQ0oaCXulnPlDlA9Rzl34KSFCfHf8A8qOL3DLh4346f6wff7izfTyqVp8sO9w55c3Xvr7wEj8LMbbiHDjH8ZaAHW63xQykfnSBo8x3T3cXH8VkqIgLzG8UueM4g8ZrtdqV7H26j1b6BzR9uGIu+PfrzOL3D5OA9F6Bcbswx3D+H9wqciqqljK2J9HT01I7VVVSPaWiOEfpHff07qH/AA4eHKy2tz8yzjG6NtfV/FQWKcGoit0R7eZ5m/Mm11O+jT2APRoaGLarwneHDHs3xWkzzL62eqoJppG09rh3G1/lvLCZJB8RBIPRuu32vRSl4nfDbbc2o5MjwmlprbksMbQ6mYGxQVrGjQaQOjJANAO7HQB9CJA8K9lrMf4BYvaLhSTUdZBFOZ4Jmlr43uqJHkEHqDt3ZBINjtFrsdqgtVmt9Lb6GnbyxU9NEI42D5AdF2tK3TC73VBoLiAPVXjQGtAHog+oiICIiAiIgIiICIiAiIgIiICIiAiIgIiIPmh7BfURAREQEREBERAREQEREBERAQjY0URBZyNLHkfqXFXU0fO3p3HZWqAiIgIiIIZ8WoNRi2G2oB7jc80tlIA3Q3tz3a2e32e/3KZlC/iK1VcQ+EFsLWu58pFVos5j+RZzbH3b7+nQ+il4XChN0daxWQGubCJ3U4ePMEZJaHlvcNJBG/kUFxLIyKJ0sr2sjYC5znHQaB3JPoFAfDenj4vcVDxAkpxHg+KSvo8VpgzljqqkHUtZy61oHQb9w7Fp32fH683HLb9Q8E8UqXxV95j8+/1kR/4hbQdP6/pyfZA9jo9HAqWsZsltxzH6Gw2embTW+ggbBTxN/Na0a6+59SfUklBaZ3iVgzbGarHclt8ddb6kfEx3RzHDs9jh1a4ehH9W1hXhx4VO4S41e7Ebgy4RVd3kq6WcNLX+QYo2sa8duYFrt66He/kJRRAREQFCfCsftp8SXEbM3fHS2WODGqF3fRZ+UqB8tSAf5SlbM75TYziN3yKrLfItlFLVPBOuYMYXa+861+KwDwoWOps/BK01dw2blfHy3mseW6L31Dudp1/I5P1IJVWJ8VM/sPDrFpL5fJXOc53lUdHCOaesmP2Yo2+pPv2A6lOKmf2Hh1i0l8vkrnOc7yqOjhHNPWTH7MUbfUn37AdSsD4VYBfsgymPitxWia7IHN/4Gs2+aCywnqOh7zHuXdwfn9kHCrAL9kGUx8VuK0TXZA5v/A1m3zQWWE9R0PeY9y7uD8/szQiICIuUTC92vT1QVaVnd5/BV0AAGh2RAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAVCoj7vb+KrogsUVaaLW3N/EKigKOOIfGvAeH+TMsGYV9ba55IWzxTOoZZYpWHY210YdvRaQRre1I6hvxccMafiJwtqpoGxMvdkY+toZXdOYBu5IifQOaPXs5rfTaCJePvHnCH8R+H+VYzcmZBTWCK5zyQRB8fNPLA2OAODgCAH9Sf0eb16GKeCfHa7WTi9ked5RJNday72yaJtOwHUk/M10ETAN8rQWhgHo0nuoJV1aLhWWm60t0t1Q6nraSZs9PM3W45GkFrhv1BAKD0v8PWCXDGrHW5NlbhUZpk0orrxMR1h39inb7NjB1oeux1AClFad+HrxM8ScjvFPjl1wybMJDoPq7YxsE8bf05AdRa6HqTGPmtw2kloJBaSOx9EH1ERB1uS32043ZZ71fK2Oht1PozVEgPLGCQ0E6HQbIG10tq4mcOrqB9XZ3jNS4/mMukPOO/dvNsdvZZDebbQ3i0VdpudNHVUVZC+CoheNtkY4EOB+8FeXPHPh5XcM+JFwxiqL5KZh86gqHDXn07ieR339C0/xmlBOfjT48RX+Sfhzh9YyW0xPAutbE7baqRp2ImEd2NI6n84jQ6D4tjG8UcZxLgPjeYXJ58urtVKKGhgG5qmZ0TdQRt9TvofQa6rzHUo8HeLj8Py+zXjJrMMqpbLTGltkE85Z9XtLi4vhGi3m6nq4E9uo0CA3R4VYBfsgymPitxWia7IHN/4Gs2+aCywnqOh7zHuXdwfn9maFiHCbiDZ+JGLMyGy0V1pKZzuXkr6R0JJ/iu6tePm1xHvpZegIi+taXHQGygNaXHQ7q7iYGN16+q+RRhg9z6lc0BERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQFRlhB6t6H2VZEFiQQdEaKgDxy56/FuE/7XLfIRdMmkNIwMJ5hTjRmI177azXtIfZbFvY140R+K6+a1Ujq9twdR08lWxnlsnMQMjW73yh3fW/RB5TU3DPiFUWOqvkeF376tpYzLNUvoZGsDB3cCQNgdzrelw4QY9S5XxRxrHK7zPodwuUMNQIzpxiLxzgH0PLvr6d16wKB7l4d7RRcese4kYr9HoaWGsfPdLcdtj5vLfyyQgDoS8t2zt12NdQQl3DMTxzDbLHZ8YtFLa6Jn97hZovP6TnHq53zcSV3aIgIiIOMj2Rxukkc1jGglznHQAHqV5deILOZuJXF6736AyS0Zl+i2xgBJ+jxkhmh3Bd1eR7vK9PrpQ0tzttTbq6LzqWqidDNGXEB7HDTm7HXRBIXV4vh2J4xE2PHcbtNqDRrdJSMjcfvcBs/iUHkxW0lVRVL6WtppqadmuaKZhY9uxsbB6jYIP4rcfwY8C8JveCUPELJqB14raieUU1LVDdLEI5Czm5O0hJaftbb8tjalzxHcCrFxWtBq4PJtmT0zCKW4BnSUAHUU2urmex6lvcb6g994bsXuWGcE8dxq8U30W4Ucc30iLnD+V755HnqOh+1vogkGNjI42xxtaxjQA1rRoADsAFyX1rXOOmjarxwAdX9fkgoxxueenb3V1GxrBofrXIAAaCICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIg4uY13cAqk6nH5rv1oiDgYZB6b+5cC1w7tI/BEQfEREBcgx57NKIg5tgee+gqjYGDvtyIgqgADQGkREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERB/9k="}}, "cell_type": "markdown", "id": "43bc2905", "metadata": {}, "source": ["![Systems-of-interacting-networks-or-networks-of-networks-are-a-natural-representation-of_W640.jpg](attachment:Systems-of-interacting-networks-or-networks-of-networks-are-a-natural-representation-of_W640.jpg)"]}, {"cell_type": "markdown", "id": "9a3e271c", "metadata": {}, "source": ["In the following, we introduce some local and global measures for interacting networks. \n", "The indices $i,j,k,l$ always denote subnetworks, while $v,w,p,q$ designate single vertices, and we always assume $v\\in V_i$. The formulae explicitly account\n", "for the general case $i \\ne j$, but can be nevertheless easily modified to suit the special case $i = j$. Furthermore, the term *cross* refers to the interaction between subnetworks $G_i,G_j$, whereas *internal* refers to the structure within a single subnetwork."]}, {"cell_type": "markdown", "id": "cc54fec3", "metadata": {}, "source": ["### Local Measures\n", "\n", "The **cross-degree centrality** $k_v^{ij}$ gives the number of neighbours of the vertex $v$ within subnetwork $G_j$,\n", "$$k_v^{ij}=\\sum_{q\\in V_j}A_{vq}\\,,\\quad v \\in V_i.$$\n", "\n", "The **cross-closeness centrality** $c^{ij}_v$ measures the topological closeness of $v$ to subnetwork $G_j$ along shortest paths,\n", "$$ c^{ij}_v = \\frac{N_j}{\\sum_{q\\in V_j}d_{vq}}\\,,$$\n", "where $d_{vq}$ is the shortest path length between vertices $v$ and $q$,\n", "and $N_j$ denotes the number of vertices in the subnetwork $G_j$.\n", "\n", "For any vertex $w \\in V$, the **cross-betweenness centrality** $b^{ij}_w$ indicates its role for mediating interactions between two subnetworks $G_i$ and $G_j$,\n", "$$ b^{ij}_w= \\sum_{p\\in V_i,\\,q \\in V_j,\\,p,q\\ne w}\\frac{\\sigma_{pq}(w)}{\\sigma_{pq}}= b^{ji}_w\\,,$$\n", "where $\\sigma_{pq}(w)$ denotes the total number of shortest paths from $p \\in V_i$ to $q \\in V_j$ that include $w$.\n", "\n", "### Global Measures\n", "\n", "The **cross-edge density** $\\rho_{ij}$ measures the density of connections between distinct subnetworks $G_i$ and $G_j$,\n", "$$ \\rho_{ij}=\\frac{|E_{ij}|}{N_i N_j}= \\rho_{ji}\\,.$$\n", "\n", "The **cross-average path length** $\\mathcal{L}_{ij}$ measures the average length of existing shortest paths between two subnetworks $G_i$ and $G_j$,\n", "$$ \\mathcal{L}_{ij}= \\frac{1}{N_i N_j - M_{ij}}\\sum_{v\\in V_i,\\,q\\in V_j,\\,d_{vq}<\\infty}d_{vq}= \\mathcal{L}_{ji}\\,, $$\n", "where $M_{ij}$ is the number of pairs $(v,q)\\in V_i\\times V_j$ which are not mutually reachable.\n", "\n", "The **global cross-clustering coefficient** $\\mathcal{C}_{ij}$ is an estimate of the probability of vertices from subnetwork $G_i$ to have mutually connected neighbours within subnetwork $G_j$,\n", "$$ \\mathcal{C}_{ij} = \\langle \\mathcal{C}^{ij}_v \\rangle_{v\\in V_i} = \\frac{1}{N_i}\\sum_{v\\in V_i,\\, k_v^{ij}>1}\\frac{\\sum_{p\\ne q \\in V_j}A_{vp}A_{pq}A_{qv}}{\\sum_{p \\ne  q \\in V_j}A_{vp}A_{vq}}\\,.$$\n", "\n", "The **cross-transitivity** $\\mathcal{T}_{ij}$ is the conditional probability that two vertices in subnetwork $G_j$ are connected if they have a common neighbour in subnetwork $G_i$,\n", "$$ \\mathcal{T}_{ij}= \\frac{\\sum_{v\\in V_i,\\,p\\ne q \\in V_j}A_{vp}A_{pq}A_{qv}}{\\sum_{v\\in V_i,\\,p\\ne q \\in V_j}A_{vp}A_{vq}}\\,.$$"]}, {"cell_type": "markdown", "id": "75c19d62", "metadata": {}, "source": ["## Application: Vertical Dynamical Structure of the Earth’s Atmosphere"]}, {"attachments": {}, "cell_type": "markdown", "id": "9a58a8a0-4c84-4377-bb78-2a54eeb5e47a", "metadata": {}, "source": ["In the following, coupled climate network analysis is illustrated by the example of the dynamical structure of the Earth's atmosphere. \n", "In order to treat a climate network as a network of networks, an *ab initio* physical separation of the climatological fields is necessary, i.e., a separation of processes into those responsible for internal coupling within a single field and those mediating interactions between fields.\n", "\n", "For the Earth system, there are distinct physical processes behind *quasi-horizontal* and *vertical* atmospheric dynamics: We have a stable isobaric quasi-horizontal stratification, while local heating of the Earth’s surface and atmosphere induces minor disturbances of the system. Therefore, we can treat the considered climatological field variables at different isobaric quasi-horizontal surfaces as separated subnetworks of an interconnected network.\n", "The small vertical disturbances of the system due to convection processes lead to vertical movement, resulting in pressure gradients that are balanced by quasi-horizontal geostrophic winds along isobares."]}, {"cell_type": "markdown", "id": "61de5550", "metadata": {}, "source": ["We consider the discretised and vertically resolved geopotential height field $Z^i_v(t)$, sampled at predefined points $v$ on isobaric surfaces $i$, as the climatological field variable to construct coupled climate networks. It reflects global weather and climate dynamics to a good approximation, as it captures the dynamics both of the geostrophic wind field and of convection processes.\n", "We specifically focus on the interaction structure between near ground and upper level atmospheric dynamics, which is particularly interesting because a large portion of the solar forcing that drives atmospheric dynamics takes place on the Earth’s surface."]}, {"attachments": {"Illustration-of-a-coupled-climate-subnetwork-as-it-is-constructed-in-this-work-where-V1_W640.jpg": {"image/jpeg": "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"}}, "cell_type": "markdown", "id": "58970dbf", "metadata": {}, "source": ["![Illustration-of-a-coupled-climate-subnetwork-as-it-is-constructed-in-this-work-where-V1_W640.jpg](attachment:Illustration-of-a-coupled-climate-subnetwork-as-it-is-constructed-in-this-work-where-V1_W640.jpg)"]}, {"cell_type": "markdown", "id": "668d5926", "metadata": {}, "source": ["## Computing a Coupled Climate Network\n", "### Loading the Climate Data"]}, {"cell_type": "markdown", "id": "0fe0dac8", "metadata": {}, "source": ["For this tutorial, we download [Reanalysis 1 data](https://psl.noaa.gov/data/gridded/data.ncep.reanalysis.html) provided by the *National Center for Environmental Prediction / National Center for Atmospheric Research* (NCEP-NCAR). This data set contains the monthly averaged geographical height potential for 17 isobaric surfaces $P_i$ of the atmosphere, on an equally spaced spherical grid with a latitude and longitude resolution of $2.5° \\!\\times\\! 2.5°$."]}, {"cell_type": "code", "execution_count": 1, "id": "d5602280-eeae-4de7-9d9f-20f08471b3e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["./data/hgt.mon.mean 100%[===================>] 296.75M  5.21MB/s    in 28s     \n", "2024-02-05 03:21:41 URL:https://downloads.psl.noaa.gov/Datasets/ncep.reanalysis/Monthlies/pressure/hgt.mon.mean.nc [311163603/311163603] -> \"./data/hgt.mon.mean.nc\" [1]\n"]}], "source": ["DATA_NAME = \"hgt.mon.mean.nc\"\n", "DATA_URL = f\"https://downloads.psl.noaa.gov/Datasets/ncep.reanalysis/Monthlies/pressure/{DATA_NAME}\"\n", "DATA_FILE = f\"./data/{DATA_NAME}\"\n", "![ -f {DATA_FILE} ] || wget -O {DATA_FILE} -nv --show-progress \"{DATA_URL}\""]}, {"cell_type": "markdown", "id": "0ddc28ac-3014-4295-afd8-f451ff6cd641", "metadata": {}, "source": ["Now we will start with some imports and some specifications regarding the data set."]}, {"cell_type": "code", "execution_count": 2, "id": "695c5769", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from pyunicorn import climate\n", "from matplotlib import pyplot as plt"]}, {"cell_type": "code", "execution_count": 3, "id": "2bd80f13-10bd-4865-8f65-55ec8c2b4f3f", "metadata": {}, "outputs": [], "source": ["#  Indicate data source (optional)\n", "DATA_SOURCE = \"NCEP-NCAR Reanalysis 1\"\n", "#  Type of data file (\"NetCDF\" indicates a NetCDF file with data on a regular\n", "#  lat-lon grid, \"iNetCDF\" allows for arbitrary grids - > see documentation).\n", "FILE_TYPE = \"NetCDF\"\n", "#  Name of observable in NetCDF file (\"hgt\" indicates the monthly mean of the geopotential height\n", "#  in the NCEP/NCAR reanalysis data)\n", "OBSERVABLE_NAME = \"hgt\"\n", "#  Select a region in time and space from the data.\n", "#  If the boundaries are equal, the data's full range is selected.\n", "#  For this tutorial, we choose a window for latitude and longitude corresponding to North America.\n", "WINDOW = {\"time_min\": 0., \"time_max\": 0., \"lat_min\": 45, \"lon_min\": 80,\n", "          \"lat_max\": 60, \"lon_max\": 120} \n", "#  Indicate the length of the annual cycle in the data (e.g., 12 for monthly\n", "#  data). This is used for calculating climatological anomaly values.\n", "TIME_CYCLE = 12"]}, {"cell_type": "markdown", "id": "67822736", "metadata": {}, "source": ["We will first generate a separate `pyunicorn.climate.ClimateData` instance from our data file for each of the 17 vertical levels, which will then be coupled to the near ground level in the following steps. "]}, {"cell_type": "code", "execution_count": 4, "id": "33aead33-118a-4e55-a96a-e69c5f3036ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading NetCDF File and converting data to NumPy array...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n", "Reading NetCDF File and converting data to NumPy array...\n"]}], "source": ["# number of total vertical levels in the data file\n", "VERTICAL_LEVELS = 17\n", "# loop over all levels to create a `ClimateData` object for each level\n", "data = np.array([climate.ClimateData.Load(\n", "    file_name=DATA_FILE, observable_name=OBSERVABLE_NAME,\n", "    data_source=DATA_SOURCE, file_type=FILE_TYPE,\n", "    window=WINDOW, time_cycle=TIME_CYCLE,\n", "    #  The `vertical_level` argument indicates the vertical level to be extracted from the data file,\n", "    #  and is ignored for horizontal data sets. If `None`, the first level in the data file is chosen.\n", "    vertical_level=l) for l in range(VERTICAL_LEVELS)])"]}, {"cell_type": "markdown", "id": "a7b51a1a", "metadata": {}, "source": ["One can use the `print()` function on a `ClimateData` object in order to show some information about the data."]}, {"cell_type": "code", "execution_count": 5, "id": "0afb1744", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Global attributes:\n", "description:  Data from NCEP initialized reanalysis (4x/day).  These are interpolated to pressure surfaces from model (sigma) surfaces.\n", "platform: Model\n", "Conventions: COARDS\n", "NCO: 20121012\n", "history: Created by NOAA-CIRES Climate Diagnostics Center (SAC) from the NCEP\n", "reanalysis data set on 07/07/97 by calc.mon.mean.year.f using\n", "/Datasets/nmc.reanalysis.derived/pressure/hgt.mon.mean.nc\n", "from /Datasets/nmc.reanalysis/pressure/hgt.79.nc to hgt.95.nc\n", "Converted to chunked, deflated non-packed NetCDF4 2014/09\n", "title: monthly mean hgt from the NCEP Reanalysis\n", "dataset_title: NCEP-NCAR Reanalysis 1\n", "References: http://www.psl.noaa.gov/data/gridded/data.ncep.reanalysis.derived.html\n", "Variables (size):\n", "level (17)\n", "lat (73)\n", "lon (144)\n", "time (913)\n", "hgt (913)\n", "ClimateData:\n", "Data: 119 grid points, 108647 measurements.\n", "Geographical boundaries:\n", "         time     lat     lon\n", "   min 1297320.0   45.00   80.00\n", "   max 1963536.0   60.00  120.00\n"]}], "source": ["print(data[0])"]}, {"cell_type": "markdown", "id": "9eea2cec", "metadata": {}, "source": ["### Constructing the Network"]}, {"cell_type": "markdown", "id": "fb13be4b", "metadata": {}, "source": ["The `pyunicorn.climate.CoupledClimateNetwork` class provides the functionality to generate a complex network from a similarity measure matrix of two time series, e.g., arising from two different observables or from one observable at two vertical levels. The idea of coupled climate networks is based on the concept of coupled patterns, for a review refer to [<PERSON><PERSON><PERSON><PERSON> et al. (1992)](https://journals.ametsoc.org/view/journals/clim/5/6/1520-0442_1992_005_0541_aiomff_2_0_co_2.xml). The two observables (layers) need to have the same time grid (temporal sampling points). More information on the construction of a `ClimateNetwork` based on various similarity measures is provided in the tutorial on [Climate Networks](https://github.com/pik-copan/pyunicorn/blob/master/examples/tutorials/ClimateNetworks.ipynb)."]}, {"cell_type": "markdown", "id": "e5e230be", "metadata": {}, "source": ["For our example, we construct 17 coupled climate networks from the data by coupling the lowest level with each other level, based on Pearson correlation without lag and with a fixed threshold. For the construction of a coupled climate network, one needs to set either the threshold $\\beta$ or the link denisty. In this example, we set the threshold $\\beta = 0.5$."]}, {"cell_type": "code", "execution_count": 6, "id": "8872d035", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Desktop/23_H2_PIK/pyunicorn/src/pyunicorn/core/interacting_networks.py:955: RuntimeWarning: invalid value encountered in scalar divide\n", "  average_path_length = path_lengths.sum() / norm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n", "Calculating daily (monthly) anomaly values...\n", "Calculating correlation matrix at zero lag from anomaly values...\n", "Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "Calculating path lengths...\n", "Calculating all shortest path lengths...\n", "Calculating n.s.i. betweenness...\n"]}], "source": ["#  for setting a fixed threshold\n", "THRESHOLD = 0.5\n", "\n", "cross_link_density = []\n", "cross_average_path_length = []\n", "cross_global_clustering = []\n", "cross_transitivity = []\n", "cross_degree = []\n", "cross_closeness = []\n", "cross_betweenness = []\n", "\n", "for l in range(VERTICAL_LEVELS):\n", "    # generate a coupled climate network between the ground level and the level l\n", "    coupled_network = climate.CoupledTsonisClimateNetwork(data[0], data[l], threshold=THRESHOLD)\n", "\n", "    # calculate global measures\n", "    cross_link_density.append(coupled_network.cross_link_density())\n", "    cross_average_path_length.append(coupled_network.cross_average_path_length())\n", "    cross_global_clustering.append(coupled_network.cross_global_clustering())\n", "    cross_transitivity.append(coupled_network.cross_transitivity())\n", "\n", "    # calculate local measures\n", "    cross_degree.append(coupled_network.cross_degree())\n", "    cross_closeness.append(coupled_network.cross_closeness())\n", "    cross_betweenness.append(coupled_network.cross_betweenness())"]}, {"cell_type": "markdown", "id": "583b18dd", "metadata": {}, "source": ["## Plotting some Coupled Network Measures\n", "### Global Measures"]}, {"cell_type": "markdown", "id": "3b27db00", "metadata": {}, "source": ["We first determine the average geopotential height for each vertical level (in *km*),\n", "which we will use as an independent variable to plot the global measures that we calculated for the 17 coupled climate networks."]}, {"cell_type": "code", "execution_count": 7, "id": "c1ead3e6", "metadata": {}, "outputs": [], "source": ["# read out the observable hgt (geopotentential height) and average it for each level\n", "hgt_averaged = np.array([\n", "    np.round(data[l].observable().flatten().mean() / 1000, 1)\n", "    for l in range(VERTICAL_LEVELS)])"]}, {"cell_type": "code", "execution_count": 8, "id": "6cd75bf5-8c14-4705-920e-8adcbfaeee15", "metadata": {}, "outputs": [], "source": ["def plot_global_coupling(\n", "    measure: np.n<PERSON><PERSON>, title: str, xlabel: str, xindex: str,\n", "    set_ylabel=True, ax=None):\n", "    \"\"\"\n", "    Plot the geopotential height over a coupling network measure.\n", "    \"\"\"\n", "    ax_ = plt if ax is None else ax\n", "    ax_p = lambda p: getattr(*((plt, p) if ax is None else (ax, f\"set_{p}\")))\n", "    ax_.plot(measure, hgt_averaged, 'o', color='blue')\n", "    ax_p(\"xlabel\")(r\"${}\".format(xlabel) + xindex + r\"$\")\n", "    if set_ylabel:\n", "        ax_p(\"ylabel\")(r\"$Z_{\\,l}$ (km)\")\n", "    ax_p(\"title\")(title)"]}, {"attachments": {}, "cell_type": "markdown", "id": "c8006820-d57b-41a2-ace7-f5355836ef5f", "metadata": {}, "source": ["Note that the cross-link density and cross-average path length are symmetrical ($\\rho_{1l}=\\rho_{l1}$, $\\mathcal{L}_{1l} = \\mathcal{L}_{l1}$), but the global cross-clustering coefficient and cross-transitivity are not ($\\mathcal{C}_{1l} \\neq \\mathcal{C}_{l1}$, $\\mathcal{T}_{1l} \\neq \\mathcal{T}_{l1}$). When computing asymmetric measures for two coupled climate subnetworks $G_i$ and $G_j$, the methods in `climate.CoupledClimateNetwork` return tuples such as $(\\mathcal{C}_{ij},\\mathcal{C}_{ji})$ or $(\\mathcal{T}_{ij},\\mathcal{T}_{ji})$."]}, {"cell_type": "code", "execution_count": 9, "id": "67126dbc-54e4-4b20-a227-9569b1c2d609", "metadata": {}, "outputs": [], "source": ["def plot_global_symmetric_coupling(measure, title, xlabel):\n", "    plot_global_coupling(measure, title, xlabel, r\"_{\\,1\\,l}\")\n", "\n", "def plot_global_asymmetric_coupling(measure, title, xlabel):\n", "    fig, axes = plt.subplots(1, 2, layout=\"constrained\")\n", "    fig.suptitle(title)\n", "    # plot both vertical directions of coupling\n", "    for vert in range(2):\n", "        plot_global_coupling(\n", "            measure[:, vert],\n", "            f\"pointing {['up', 'down'][vert]}wards\", xlabel,\n", "            [r\"_{\\,1\\,l}\", r\"_{\\,l\\,1}\"][vert],\n", "            set_ylabel=(vert == 0), ax=axes[vert])"]}, {"cell_type": "markdown", "id": "05c2ddbb", "metadata": {}, "source": ["#### Cross-<PERSON> Density"]}, {"cell_type": "code", "execution_count": 10, "id": "2a017cc2-9fee-4447-8cd6-2cd055f9bad2", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_global_symmetric_coupling(cross_link_density, \"cross-link density\", r\"\\rho\")"]}, {"cell_type": "markdown", "id": "a20a0dc7", "metadata": {}, "source": ["#### Cross-Average Path Length"]}, {"cell_type": "code", "execution_count": 11, "id": "4ea4c9b0-f483-4646-b61a-52ce1ddd8ba4", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjsAAAHHCAYAAABZbpmkAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/H5lhTAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA2MUlEQVR4nO3deXhU9b3H8c9kSEJAGPYsJAIqgqylIIgQCKAoogZjoEJbFm9dEQn0VsC2KlaNWGRpqyAuYAXccKAKinAlifGCGgREQNFeEQMmpSIkLBKT4Xf/mGZgmCRkmWRmTt6v55nHZ37nO2e+czgyH87yG5sxxggAAMCiwgLdAAAAQG0i7AAAAEsj7AAAAEsj7AAAAEsj7AAAAEsj7AAAAEsj7AAAAEsj7AAAAEsj7AAAAEsj7ABALVm5cqUWLFjgM/7NN9/IZrNp7ty51VpvZmambDabMjMza9agn+zZs0cPPfSQvvnmG59lSUlJ6tatW903BZyFsAMAtaS8sGM1e/bs0ezZs8sMO0AwIOwAteDHH38UPztXtpMnTwa6BQD1DGEHOI8vvvhCY8eOVXR0tCIjI3XhhRdq/PjxKioqkiQtW7ZMNptNGzZs0K233qrWrVurUaNGKioq0unTp/XEE0+oc+fOioyMVJs2bTR+/HgdOHDA6z22b9+u66+/Xm3atFFkZKTi4uI0cuRIr7rXX39d/fr1k8PhUKNGjXTRRRfp1ltvrdRnmD17tvr166cWLVqoadOm+vnPf67nn3/eK5CNGjVK7dq10+nTp31e369fP/385z/3PDfG6Omnn9bPfvYzRUVFqXnz5kpNTdXXX3/t9brSUxjvv/++rrzySjVq1MjT86uvvqrhw4crNjZWUVFRuuyyyzRz5kydOHHC5/2fffZZXXrppYqMjFSXLl20cuVKTZw4Ue3bt/eq++mnn/TII494tnfr1q01adIk/fvf/z7vNpo4caIuuOAC7d69W8OGDVPjxo3VunVr3XPPPT4B7amnntKgQYPUpk0bNW7cWN27d9cTTzyh4uJir8++bt067d+/XzabzfM417x589ShQwddcMEF6t+/vz788MPz9lqerVu36sYbb1SLFi3UsGFD9erVS6+99ppXTen+mpGRobvuukutWrVSy5YtlZKSou+++86rtqioSL/97W8VExOjRo0aadCgQfrkk0/Uvn17TZw40bO+0aNHS5KGDBni+ZzLli3zWldOTo4SExM9++7jjz9e5r4G1AoDoFw7duwwF1xwgWnfvr1ZvHixee+998zy5cvNmDFjTGFhoTHGmKVLlxpJpm3btub2228377zzjlm1apUpKSkxt99+u5Fk7rnnHrN+/XqzePFi07p1a5OQkGD+/e9/G2OMOX78uGnZsqXp06ePee2110xWVpZ59dVXzZ133mn27NljjDFm8+bNxmazmVtuucW8/fbbZtOmTWbp0qXm17/+daU+x8SJE83zzz9vNm7caDZu3Gj+9Kc/maioKDN79mxPzT/+8Q8jyWzcuNHrtZ9//rmRZP7yl794xm677TYTHh5ufvvb35r169eblStXms6dO5vo6GiTn5/vqRs8eLBp0aKFSUhIMH/9619NRkaGycrKMsYY86c//cnMnz/frFu3zmRmZprFixebDh06mCFDhni9/zPPPGMkmZtvvtmsXbvWrFixwlx66aWmXbt2pl27dp46l8tlrr32WtO4cWMze/Zss3HjRvPcc8+Ztm3bmi5dupiTJ09WuI0mTJhgIiIizIUXXmgeffRRs2HDBvPQQw+ZBg0amOuvv96rdtq0aWbRokVm/fr1ZtOmTWb+/PmmVatWZtKkSZ6a3bt3mwEDBpiYmBizZcsWz8MYY/bt22ckmfbt25trr73WrFmzxqxZs8Z0797dNG/e3Bw9erTCXjMyMowkk5GR4RnbtGmTiYiIMImJiebVV18169evNxMnTjSSzNKlSz11pfvrRRddZKZMmWLeffdd89xzz5nmzZv7bPuxY8easLAwM3PmTLNhwwazYMECk5CQYBwOh5kwYYIxxphDhw6Zxx57zEgyTz31lOdzHjp0yBjj3gdatmxpOnbsaBYvXmw2btxo7r77biPJvPjiixV+TsBfCDtABYYOHWqaNWvm+Yu7LKVfHuPHj/caLw0Jd999t9f4Rx99ZCSZ+++/3xhjzNatW40ks2bNmnLfY+7cuUbSeb8EK8Plcpni4mLz8MMPm5YtW5rTp08bY4wpLi420dHRZty4cV719913n4mIiDDff/+9McaYLVu2GEnmySef9KrLzc01UVFR5r777vOMDR482Egy7733XoU9nT592hQXF5usrCwjyXz66aeeXmNiYky/fv286vfv32/Cw8O9ws7LL79sJJk33njDqzYnJ8dIMk8//XSFPUyYMMFIMgsXLvQaf/TRR40k88EHH5T5utLt+fe//93Y7Xbzww8/eJaNHDnSq8dSpWGne/fupqSkxDP+8ccfG0nm5ZdfrrDXssJO586dTa9evUxxcbFX7fXXX29iY2ONy+UyxpzZX8/dL5944gkjyeTl5Rlj3GFNkpkxY4ZXXel2Lg07xhjz+uuv+/RTqnQf+Oijj7zGu3TpYq655poKPyfgL5zGAspx8uRJZWVlacyYMWrduvV562+++Wav5xkZGZLkOdxfqm/fvrrsssv03nvvSZIuueQSNW/eXDNmzNDixYu1Z88en3VffvnlkqQxY8botdde08GDB31qXC6XSkpKPI+zTxFs2rRJV111lRwOh+x2u8LDw/XAAw/o8OHDOnTokCSpQYMG+tWvfiWn06mCggLPOl966SUlJyerZcuWkqS1a9fKZrPpV7/6ldf7xcTEqGfPnj53CDVv3lxDhw716ffrr7/WuHHjFBMT4+lp8ODBkqTPP/9ckrR3717l5+drzJgxXq+98MILNWDAAK+xtWvXqlmzZrrhhhu8+vrZz36mmJiYSt+59Mtf/tLr+bhx4ySd+fOU3Kcdb7zxRrVs2dLT+/jx4+VyufTll19W6n0kaeTIkbLb7Z7nPXr0kCTt37+/0uuQpH/+85/64osvPL2f/fmvu+465eXlae/evV6vufHGG72en/veWVlZkuSz7VNTU9WgQYMq9RcTE6O+ffv6vF9VPydQXYQdoBxHjhyRy+VSfHx8pepjY2O9nh8+fLjMcUmKi4vzLHc4HMrKytLPfvYz3X///eratavi4uL04IMPeq4BGTRokNasWaOSkhKNHz9e8fHx6tatm15++WXPOi+++GKFh4d7Hg8//LAk6eOPP9bw4cMlua99+d///V/l5OTo97//vST3xdSlbr31Vp06dUqvvPKKJOndd99VXl6eJk2a5Kn517/+JWOMoqOjvd4vPDxcH374ob7//vsKt4skHT9+XImJifroo4/0yCOPKDMzUzk5OXI6nV49lW6j6Ohon3WcO/avf/1LR48eVUREhE9f+fn5Pn2VpUGDBp5QVyomJsarl2+//VaJiYk6ePCgFi5cqOzsbOXk5Oipp57y6r0yzn2vyMjIKq9Dcn92Sfrv//5vn89+9913S5LP5z/fe5e37cvaRudTVn1kZGSVPydQXVWL50A90qJFC9ntdp+Lictz7sWnpX/B5+Xl+QSm7777Tq1atfI87969u1555RUZY7Rz504tW7ZMDz/8sKKiojRz5kxJUnJyspKTk1VUVKQPP/xQ6enpGjdunNq3b6/+/fvrrbfe8lw0LbkDlSS98sorCg8P19q1a9WwYUPP8jVr1vh8hi5duqhv375aunSp7rjjDi1dulRxcXGesCRJrVq1ks1mU3Z2tucL8mznjpV1Ue6mTZv03XffKTMz03M0R5KOHj1a5jYs/TI/W35+vtfz0gtt169f71MrSU2aNClz/GwlJSU6fPiw15dz6fuUjq1Zs0YnTpyQ0+lUu3btPHU7duw47/prS+m+NGvWLKWkpJRZ06lTpyqt8+xt37ZtW8946TYCQglHdoByREVFafDgwXr99dcrdVTgXKWnbpYvX+41npOTo88//1zDhg3zeY3NZlPPnj01f/58NWvWTNu2bfOpiYyM1ODBgzVnzhxJ7lMqkjsw9enTx/MoDTs2m00NGjTwOl3y448/6qWXXiqz70mTJumjjz7SBx98oLfeeksTJkzweu31118vY4wOHjzo9X6lj+7du59325QGoHOD0TPPPOP1vFOnToqJifG5o+jbb7/V5s2bvcauv/56HT58WC6Xq8y+Kvtlv2LFCq/nK1eulOS+u6q83o0xevbZZ33WVVdHLzp16qSOHTvq008/LfOz9+nTp1Jh72yDBg2S5L5r7myrVq1SSUmJ11h1j0gBdYUjO0AF5s2bp4EDB6pfv36aOXOmLrnkEv3rX//Sm2++qWeeeabCL5BOnTrp9ttv11//+leFhYVpxIgR+uabb/THP/5RCQkJmjZtmiT3tSZPP/20Ro0apYsuukjGGDmdTh09elRXX321JOmBBx7QgQMHNGzYMMXHx+vo0aNauHCh13Uu5Rk5cqTmzZuncePG6fbbb9fhw4c1d+7cMo/KSNLYsWM1ffp0jR07VkVFRT7XHA0YMEC33367Jk2apK1bt2rQoEFq3Lix8vLy9MEHH6h79+666667KuzpyiuvVPPmzXXnnXfqwQcfVHh4uFasWKFPP/3Uqy4sLEyzZ8/WHXfcodTUVN166606evSoZs+erdjYWIWFnfn32i233KIVK1bouuuu09SpU9W3b1+Fh4frwIEDysjIUHJysm666aYK+4qIiNCTTz6p48eP6/LLL9fmzZv1yCOPaMSIERo4cKAk6eqrr1ZERITGjh2r++67T6dOndKiRYt05MgRn/V1795dTqdTixYtUu/evRUWFqY+ffpU2EN1PfPMMxoxYoSuueYaTZw4UW3bttUPP/ygzz//XNu2bdPrr79epfV17dpVY8eO1ZNPPim73a6hQ4dq9+7devLJJ+VwOLy2fekMyUuWLFGTJk3UsGFDdejQocqnu4BaE9DLo4EQsGfPHjN69GjTsmVLz63JEydONKdOnTLGnLm7JScnx+e1LpfLzJkzx1x66aUmPDzctGrVyvzqV78yubm5npovvvjCjB071lx88cUmKirKOBwO07dvX7Ns2TJPzdq1a82IESNM27ZtTUREhGnTpo257rrrTHZ2dqU+wwsvvGA6depkIiMjzUUXXWTS09PN888/bySZffv2+dSPGzfOSDIDBgyocJ39+vUzjRs3NlFRUebiiy8248ePN1u3bvXUDB482HTt2rXM12/evNn079/fNGrUyLRu3dr85je/Mdu2bfO5VdoYY5YsWWIuueQSExERYS699FLzwgsvmOTkZNOrVy+vuuLiYjN37lzTs2dP07BhQ3PBBReYzp07mzvuuMN89dVXFW6jCRMmmMaNG5udO3eapKQkExUVZVq0aGHuuusuc/z4ca/at956y/Mebdu2Nb/73e/MO++843NH0g8//GBSU1NNs2bNjM1mM6V/5ZbejfXnP//Zpw9J5sEHH6yw17LuxjLGmE8//dSMGTPGtGnTxoSHh5uYmBgzdOhQs3jxYk9NeftrWes8deqUmT59umnTpo1p2LChueKKK8yWLVuMw+Ew06ZN83r9ggULTIcOHYzdbvf6MyxvH5gwYUKZd6oBtcFmDNO8AggtR48e1aWXXqpRo0ZpyZIlflnnxIkTtWrVKh0/ftwv67OqzZs3a8CAAVqxYoXnTjUg2HEaC0BQy8/P16OPPqohQ4aoZcuW2r9/v+bPn69jx45p6tSpgW7P0jZu3KgtW7aod+/eioqK0qeffqrHH39cHTt2LPdCaCAYEXYABLXIyEh98803uvvuu/XDDz+oUaNGuuKKK7R48WJ17do10O1ZWtOmTbVhwwYtWLBAx44dU6tWrTRixAilp6d73dkHBDtOYwEAAEvj1nMAAGBphB0AAGBphB0AAGBp9eoC5dOnT+u7775TkyZNypzCHgAABB9jjI4dO6a4uDivCS0rq16Fne+++04JCQmBbgMAAFRDbm5upX+c+WxBFXbef/99/fnPf9Ynn3yivLw8rV69WqNGjZIkFRcX6w9/+IPefvttff3113I4HLrqqqv0+OOPe34D6HxKp/bPzc1V06ZNa+tjAAAAPyosLFRCQkKVf+OtVFCFnRMnTqhnz56aNGmSbr75Zq9lJ0+e1LZt2/THP/5RPXv21JEjR5SWlqYbb7xRW7durdT6S09dNW3alLADAECIqe4lKEE7z47NZvM6slOWnJwc9e3bV/v379eFF1543nUWFhbK4XCooKCAsAMAQIio6fd3UB3ZqaqCggLZbDY1a9aszOVFRUUqKiryPC8sLKyjzgAAQLAI2VvPT506pZkzZ2rcuHHlprz09HQ5HA7Pg4uTAQCof0Iy7BQXF+uWW27R6dOn9fTTT5dbN2vWLBUUFHgeubm5ddglAAAIBiF3Gqu4uFhjxozRvn37tGnTpgrP3UVGRioyMrIOuwMAAMEmpMJOadD56quvlJGRoZYtWwa6JQAAEOSCKuwcP35c//znPz3P9+3bpx07dqhFixaKi4tTamqqtm3bprVr18rlcik/P1+S1KJFC0VERASqbQAAEMSC6tbzzMxMDRkyxGd8woQJeuihh9ShQ4cyX5eRkaGkpKTzrp9bzwEACD2WuvU8KSlJFWWvIMplAAAgRARV2AEAAKHJ5ZKys6W8PCk2VkpMlOz2QHflRtgBAAA14nRKU6dKBw6cGYuPlxYulFJSAtdXqZCcZwcAAAQHp1NKTfUOOpJ08KB73OkMTF9nI+wAAIBqcbncR3TKuqS2dCwtzV0XSIQdAABQLdnZvkd0zmaMlJvrrgskwg4AAKiWvDz/1tUWwg4AAKiW2Fj/1tUWwg4AAKiWxET3XVc2W9nLbTYpIcFdF0iEHQAAUC12u/v2csk38JQ+X7Ag8PPtEHYAAEC1paRIq1ZJbdt6j8fHu8eDYZ4dJhUEAAA1kpIiJSczgzIAALAwu12qxG9yBwSnsQAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKU1CHQDAABrcbmk7GwpL0+KjZUSEyW7PdBdoT4j7AAA/MbplKZOlQ4cODMWHy8tXCilpASuL9RvnMYCAPiF0ymlpnoHHUk6eNA97nQGpi+AsAMAqDGXy31ExxjfZaVjaWnuOqCuEXYAADWWne17ROdsxki5ue46oK4RdgAANZaX5986wJ8IOwCAGouN9W8d4E9BFXbef/993XDDDYqLi5PNZtOaNWu8lhtj9NBDDykuLk5RUVFKSkrS7t27A9MsAMAjMdF915XNVvZym01KSHDXAXUtqMLOiRMn1LNnT/3tb38rc/kTTzyhefPm6W9/+5tycnIUExOjq6++WseOHavjTgEAZ7Pb3beXS76Bp/T5ggXMt4PAsBlT1rXzgWez2bR69WqNGjVKkvuoTlxcnNLS0jRjxgxJUlFRkaKjozVnzhzdcccd511nYWGhHA6HCgoK1LRp09psHwDqpbLm2UlIcAcd5tlBddX0+ztkJhXct2+f8vPzNXz4cM9YZGSkBg8erM2bN5cZdoqKilRUVOR5XlhYWCe9AkB9lZIiJSczgzKCS8iEnfz8fElSdHS013h0dLT2799f5mvS09M1e/bsWu8NAHCG3S4lJQW6C+CMoLpmpzJs55wMNsb4jJWaNWuWCgoKPI/c3Ny6aBEAAASRkDmyExMTI8l9hCf2rHsXDx065HO0p1RkZKQiIyPrpD8AABCcQubITocOHRQTE6ONGzd6xn766SdlZWXpyiuvDGBnAAAgmAXVkZ3jx4/rn//8p+f5vn37tGPHDrVo0UIXXnih0tLS9Nhjj6ljx47q2LGjHnvsMTVq1Ejjxo0LYNcAACCYBVXY2bp1q4YMGeJ5Pn36dEnShAkTtGzZMt1333368ccfdffdd+vIkSPq16+fNmzYoCZNmgSqZQAAEOSCdp6d2sA8OwAAhJ6afn+HzDU7AAAA1UHYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAlkbYAQAAltYg0A0AAOqGyyVlZ0t5eVJsrJSYKNntge4KqH2EHQCoB5xOaepU6cCBM2Px8dLChVJKSuD6AuoCp7EAwOKcTik11TvoSNLBg+5xpzMwfQF1hbADABbmcrmP6Bjju6x0LC3NXQdYFWEHACwsO9v3iM7ZjJFyc911gFURdgDAwvLy/FsHhCLCDgBYWGysf+uAUETYAQALS0x033Vls5W93GaTEhLcdYBVEXYAwMLsdvft5ZJv4Cl9vmAB8+3A2gg7AGBxKSnSqlVS27be4/Hx7nHm2YHVMakgANQDKSlScjIzKKN+IuwAQD1ht0tJSYHuAqh7nMYCAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWFlJhp6SkRH/4wx/UoUMHRUVF6aKLLtLDDz+s06dPB7o1AAAQpBoEuoGqmDNnjhYvXqwXX3xRXbt21datWzVp0iQ5HA5NnTo10O0BAIAgFFJhZ8uWLUpOTtbIkSMlSe3bt9fLL7+srVu3BrgzAAAQrELqNNbAgQP13nvv6csvv5Qkffrpp/rggw903XXXlVlfVFSkwsJCrwcAAKhfQurIzowZM1RQUKDOnTvLbrfL5XLp0Ucf1dixY8usT09P1+zZs+u4SwAAEExC6sjOq6++quXLl2vlypXatm2bXnzxRc2dO1cvvvhimfWzZs1SQUGB55Gbm1vHHQMAgECzGWNMoJuorISEBM2cOVOTJ0/2jD3yyCNavny5vvjii/O+vrCwUA6HQwUFBWratGlttgoAAPykpt/fIXVk5+TJkwoL827Zbrdz6zkAAChXSF2zc8MNN+jRRx/VhRdeqK5du2r79u2aN2+ebr311kC3BgAAglRIncY6duyY/vjHP2r16tU6dOiQ4uLiNHbsWD3wwAOKiIg47+s5jQUAQOip6fd3SIWdmiLsAAAQeurVNTsAAABVRdgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACWRtgBAACW1iDQDQBAfeFySdnZUl6eFBsrJSZKdnuguwKsj7ADAHXA6ZSmTpUOHDgzFh8vLVwopaQEri+gPuA0FgDUMqdTSk31DjqSdPCge9zpDExfQH1B2AGAWuRyuY/oGOO7rHQsLc1dB6B2EHYAoBZlZ/se0TmbMVJurrsOQO0g7ABALcrL828dgKoj7ABALYqN9W8dgKoj7ABALUpMdN91ZbOVvdxmkxIS3HUAagdhBwBqkd3uvr1c8g08pc8XLGC+HaA2EXYAoJalpEirVklt23qPx8e7x5lnB6hdTCoIAHUgJUVKTmYGZSAQCDsAUEfsdikpKdBdAPUPp7EAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClVfvW8+LiYuXn5+vkyZNq3bq1WrRo4c++AAAA/KJKR3aOHz+uZ555RklJSXI4HGrfvr26dOmi1q1bq127drrtttuUk5NTW70CAABUWaXDzvz589W+fXs9++yzGjp0qJxOp3bs2KG9e/dqy5YtevDBB1VSUqKrr75a1157rb766qva7BsAAKBSbMYYU5nC0aNH64EHHlD37t0rrDt16pReeOEFRURE6De/+Y1fmvSXwsJCORwOFRQUqGnTpoFuBwAAVEJNv78rHXasgLADAEDoqen3d7UvUD516pR27typQ4cO6fTp017LbrzxxuquFgAAwK+qFXbWr1+v8ePH6/vvv/dZZrPZ5HK5atwYAACAP1Rrnp177rlHo0ePVl5enk6fPu31IOgAAIBgUq2wc+jQIU2fPl3R0dH+7gcAAMCvqhV2UlNTlZmZ6edWAAAA/K9ad2OdPHlSo0ePVuvWrdW9e3eFh4d7Lb/33nv91qA/cTcWAAChJyB3Y61cuVLvvvuuoqKilJmZKZvN5llms9mCNuwAAID6p1ph5w9/+IMefvhhzZw5U2Fh/JYoAAAIXtVKKj/99JN+8YtfEHQAAEDQq1ZamTBhgl599VV/9wIAAOB31TqN5XK59MQTT+jdd99Vjx49fC5Qnjdvnl+aK8vBgwc1Y8YMvfPOO/rxxx916aWX6vnnn1fv3r1r7T0BAEDoqlbY+eyzz9SrVy9J0q5du/zaUEWOHDmiAQMGaMiQIXrnnXfUpk0b/d///Z+aNWtWZz0AAIDQUq2wk5GR4e8+KmXOnDlKSEjQ0qVLPWPt27cPSC8AACA0VOuaneXLl5e77He/+121mzmfN998U3369NHo0aPVpk0b9erVS88++2y59UVFRSosLPR6AACA+qXav421du1an/Fp06ZVGIRq6uuvv9aiRYvUsWNHvfvuu7rzzjt177336u9//3uZ9enp6XI4HJ5HQkJCrfUGAACCU7VmUF6/fr1uueUWvfnmmxo0aJAkacqUKXI6nXrvvffUuXNnvzcqSREREerTp482b97sGbv33nuVk5OjLVu2+NQXFRWpqKjI87ywsFAJCQnMoAwAQAip6QzK1Tqyc+2112rx4sUaNWqUtm7dqrvvvltOp1MZGRm1FnQkKTY2Vl26dPEau+yyy/Ttt9+WWR8ZGammTZt6PQAAQP1SrQuUJemWW27RkSNHNHDgQLVu3VpZWVm65JJL/NmbjwEDBmjv3r1eY19++aXatWtXq+8LAABCV6XDzvTp08scL71Q+Omnn/aM1dY8O9OmTdOVV16pxx57TGPGjNHHH3+sJUuWaMmSJbXyfgAAIPRV+pqdIUOGVG6FNps2bdpUo6YqsnbtWs2aNUtfffWVOnTooOnTp+u2226r1Gv51XMAAEJPTb+/q3WBcqgi7AAAEHoCcoEyAABAqKh02CnvjqfyHDx4sMrNAAAA+Fulw87ll1+u2267TR9//HG5NQUFBXr22WfVrVs3OZ1OvzQIAABQE5W+G+vzzz/XY489pmuvvVbh4eHq06eP4uLi1LBhQx05ckR79uzR7t271adPH/35z3/WiBEjarNvAKgRl0vKzpby8qTYWCkxUbLbA90VgNpQ5QuUT506pbffflvZ2dn65ptv9OOPP6pVq1bq1auXrrnmGnXr1q22eq0xLlAGIElOpzR1qnTgwJmx+Hhp4UIpJSVwfQEoG3djVQFhB4DTKaWmSuf+zWezuf+7ahWBBwg23I0FAJXkcrmP6JT1T7zSsbQ0dx0A6yDsAKg3srO9T12dyxgpN9ddB8A6CDsA6o28PP/WAQgNhB0A9UZsrH/rAISGGoWdadOmadmyZdq2bZuKior81RMA1IrERPddV6UXI5/LZpMSEtx1AKyj0vPslCUpKUk7d+7UunXrtHv3btntdnXt2lU9evRQjx49dP311/urTwCoMbvdfXt5aqo72Jx9oXJpAFqwgPl2AKvx663nP/74o3bt2qWdO3dq165dmj9/vr9W7Rfceg5AKnuenYQEd9DhtnMg+DDPThUQdgCUYgZlIHTU6Tw7t9xyi/bs2VPlNwGAYGO3S0lJ0tix7v8SdADrqlLYee211zR06NByA48xRoWFhX5pDAAAwB+qfDdWz549NWTIEO3evdtn2aFDh9S8eXO/NAYAAOAPVQo7NptNy5Yt09ChQzVkyBDt2rXLp6YeXQIEAABCQJXCjjFGdrtdK1as0LBhwzR06FCfwGMrbwILAACAAKjWpIJhYWFasWKFrrrqKg0dOlSfffaZv/sCAADwiyqfxvK8MCxMy5cv9wSenTt3+r05AACAmqryaSyvF/8n8Fx99dUaNmyYduzY4c/eAAAAaqxKYWfdunVyOBzeK/hP4Bk+fLhuvvlmvzYHAABQU1UKOyNGjFBkZKTvSsLC9NJLLyk5OdlvjQEAAPhDjX713GtF/znCs2XLFn+tEgAAoMb8FnYk9wXMffv29ecqAQAAasSvYQcAACDYEHYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClNQh0A0CguVxSdraUlyfFxkqJiZLdHuiuAAD+QthBveZ0SlOnSgcOnBmLj5cWLpRSUgLXFwDAfziNhXrL6ZRSU72DjiQdPOgedzoD0xcAwL8IO6iXXC73ER1jfJeVjqWluesAAKEtpMNOenq6bDab0tLSAt0KQkx2tu8RnbMZI+XmuusAAKEtZMNOTk6OlixZoh49egS6FYSgvDz/1gEAgldIhp3jx4/rl7/8pZ599lk1b9480O0gBMXG+rcOABC8QjLsTJ48WSNHjtRVV11VYV1RUZEKCwu9HoDkvr08Pl6y2cpebrNJCQnuOgBAaAu5sPPKK69o27ZtSk9PP29tenq6HA6H55GQkFAHHSIU2O3u28sl38BT+nzBAubbAQArCKmwk5ubq6lTp2r58uVq2LDheetnzZqlgoICzyM3N7cOukSoSEmRVq2S2rb1Ho+Pd48zzw4AWIPNmLJuvg1Oa9as0U033ST7Wf/cdrlcstlsCgsLU1FRkdeycxUWFsrhcKigoEBNmzati5YRAphBGQCCW02/v0NqBuVhw4bps88+8xqbNGmSOnfurBkzZlQYdIDy2O1SUlKguwAA1JaQCjtNmjRRt27dvMYaN26sli1b+owDAABIIXbNDgAAQFWF1JGdsmRmZga6BQAAEMQ4sgMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACyNsAMAACytQaAbQGhzuaTsbCkvT4qNlRITJbs90F0BAHAGYQfV5nRKU6dKBw6cGYuPlxYulFJSAtcXAABn4zQWqsXplFJTvYOOJB086B53OgPTFwAA5yLsoMpcLvcRHWN8l5WOpaW56wAACDTCDqosO9v3iM7ZjJFyc911AAAEGmEHVZaX5986AABqE2EHVRYb6986AABqE2EHVZaY6L7rymYre7nNJiUkuOsAAAg0wg6qzG53314u+Qae0ucLFjDfDgAgOBB2UC0pKdKqVVLbtt7j8fHucebZAQAECyYVRLWlpEjJycygDAAIboQd1IjdLiUlBboLAADKx2ksAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaYQdAABgaSEVdtLT03X55ZerSZMmatOmjUaNGqW9e/cGui0AABDEQirsZGVlafLkyfrwww+1ceNGlZSUaPjw4Tpx4kSgWwMAAEHKZowxgW6iuv7973+rTZs2ysrK0qBBg85bX1hYKIfDoYKCAjVt2rQOOgQAADVV0+/vBrXQU50pKCiQJLVo0aLM5UVFRSoqKvI8LywsrJO+AABA8Aip01hnM8Zo+vTpGjhwoLp161ZmTXp6uhwOh+eRkJBQx10CAIBAC9nTWJMnT9a6dev0wQcfKD4+vsyaso7sJCQkcBoLAIAQUi9PY02ZMkVvvvmm3n///XKDjiRFRkYqMjKyDjsDAADBJqTCjjFGU6ZM0erVq5WZmakOHToEuiUAABDkQirsTJ48WStXrtQ//vEPNWnSRPn5+ZIkh8OhqKioAHcHAACCUUhds2Oz2cocX7p0qSZOnHje13PrOQAAoadeXbMTQrkMAAAEiZC99RwAAKAyCDsAAMDSCDsAAMDSQuqaHStyuaTsbCkvT4qNlRITJbs90F0BAGAdhJ0AcjqlqVOlAwfOjMXHSwsXSikpgesLAAAr4TRWgDidUmqqd9CRpIMH3eNOZ2D6AgDAagg7AeByuY/olHUnfelYWpq7DgAA1AxhJwCys32P6JzNGCk3110HAABqhrATAHl5/q0DAADlI+wEQGysf+sAAED5CDsBkJjovuuqnJ/6ks0mJSS46wAAQM0QdgLAbnffXi75Bp7S5wsWMN8OAAD+QNgJkJQUadUqqW1b7/H4ePc48+wAAOAfTCoYQCkpUnIyMygDAFCbCDsBZrdLSUmB7gIAAOviNBYAALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0JhX0I5eL2ZABAAg2hB0/cTqlqVOlAwfOjMXHu3/wk9+5AgAgcDiN5QdOp5Sa6h10JOngQfe40xmYvgAAAGGnxlwu9xEdY3yXlY6lpbnrAABA3SPs1FB2tu8RnbMZI+XmuusAAEDdI+zUUF6ef+sAAIB/EXZqKDbWv3UAAMC/CDs1lJjovuvKZit7uc0mJSS46wAAQN0j7NSQ3e6+vVzyDTylzxcsYL4dAAAChbDjBykp0qpVUtu23uPx8e5x5tkBACBwmFTQT1JSpORkZlAGACDYEHb8yG6XkpIC3QUAADgbp7EAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClEXYAAIClMYOyH7hc/EwEAADBirBTQ06nNHWqdODAmbH4ePcvofMDoAAABB6nsWrA6ZRSU72DjiQdPOgedzoD0xcAADiDsFNNLpf7iI4xvstKx9LS3HUAACBwQjLsPP300+rQoYMaNmyo3r17Kzs7u857yM72PaJzNmOk3Fx3HQAACJyQCzuvvvqq0tLS9Pvf/17bt29XYmKiRowYoW+//bZO+8jL828dAACoHSEXdubNm6f/+q//0m9+8xtddtllWrBggRISErRo0SKf2qKiIhUWFno9/CU21r91AACgdoRU2Pnpp5/0ySefaPjw4V7jw4cP1+bNm33q09PT5XA4PI+EhAS/9ZKY6L7rymYre7nNJiUkuOsAAEDghFTY+f777+VyuRQdHe01Hh0drfz8fJ/6WbNmqaCgwPPIzc31Wy92u/v2csk38JQ+X7CA+XYAAAi0kAo7pWznpAtjjM+YJEVGRqpp06ZeD39KSZFWrZLatvUej493jzPPDgAAgRdSkwq2atVKdrvd5yjOoUOHfI721JWUFCk5mRmUAQAIViEVdiIiItS7d29t3LhRN910k2d848aNSk5ODlhfdruUlBSwtwcAABUIqbAjSdOnT9evf/1r9enTR/3799eSJUv07bff6s477wx0awAAIAiFXNj5xS9+ocOHD+vhhx9WXl6eunXrprffflvt2rULdGsAACAI2Ywp6wcPrKmwsFAOh0MFBQV+v1gZAADUjpp+f4fk3VgAAACVRdgBAACWRtgBAACWRtgBAACWRtgBAACWFnK3ntdE6Y1n/vz1cwAAULtKv7erewN5vQo7x44dkyS//vo5AACoG8eOHZPD4ajy6+rVPDunT5/Wd999pyZNmpT5w6H1UWFhoRISEpSbm8vcQ37CNvUvtqd/sT39j23qX2VtT2OMjh07pri4OIWFVf0KnHp1ZCcsLEzx8fGBbiMo1cavwtd3bFP/Ynv6F9vT/9im/nXu9qzOEZ1SXKAMAAAsjbADAAAsjbBTz0VGRurBBx9UZGRkoFuxDLapf7E9/Yvt6X9sU/+qje1Zry5QBgAA9Q9HdgAAgKURdgAAgKURdgAAgKURdgAAgKURdgAAgKURdizu/fff1w033KC4uDjZbDatWbOmwvrMzEzZbDafxxdffFE3DQe59PR0XX755WrSpInatGmjUaNGae/eved9XVZWlnr37q2GDRvqoosu0uLFi+ug2+BXne3JPlq+RYsWqUePHp6ZZ/v376933nmnwtewb1asqtuU/bNq0tPTZbPZlJaWVmFdTfdTwo7FnThxQj179tTf/va3Kr1u7969ysvL8zw6duxYSx2GlqysLE2ePFkffvihNm7cqJKSEg0fPlwnTpwo9zX79u3Tddddp8TERG3fvl3333+/7r33Xr3xxht12Hlwqs72LMU+6is+Pl6PP/64tm7dqq1bt2ro0KFKTk7W7t27y6xn3zy/qm7TUuyf55eTk6MlS5aoR48eFdb5ZT81qDckmdWrV1dYk5GRYSSZI0eO1ElPoe7QoUNGksnKyiq35r777jOdO3f2GrvjjjvMFVdcUdvthZzKbE/20app3ry5ee6558pcxr5ZPRVtU/bPyjl27Jjp2LGj2bhxoxk8eLCZOnVqubX+2E85soMy9erVS7GxsRo2bJgyMjIC3U7QKigokCS1aNGi3JotW7Zo+PDhXmPXXHONtm7dquLi4lrtL9RUZnuWYh+tmMvl0iuvvKITJ06of//+Zdawb1ZNZbZpKfbPik2ePFkjR47UVVdddd5af+yn9epXz3F+sbGxWrJkiXr37q2ioiK99NJLGjZsmDIzMzVo0KBAtxdUjDGaPn26Bg4cqG7dupVbl5+fr+joaK+x6OholZSU6Pvvv1dsbGxttxoSKrs92Ucr9tlnn6l///46deqULrjgAq1evVpdunQps5Z9s3Kqsk3ZP8/vlVde0bZt25STk1Open/sp4QdeOnUqZM6derked6/f3/l5uZq7ty5/I96jnvuuUc7d+7UBx98cN5am83m9dz851dazh2vzyq7PdlHK9apUyft2LFDR48e1RtvvKEJEyYoKyur3C9n9s3zq8o2Zf+sWG5urqZOnaoNGzaoYcOGlX5dTfdTTmPhvK644gp99dVXgW4jqEyZMkVvvvmmMjIyFB8fX2FtTEyM8vPzvcYOHTqkBg0aqGXLlrXZZsioyvYsC/voGREREbrkkkvUp08fpaenq2fPnlq4cGGZteyblVOVbVoW9s8zPvnkEx06dEi9e/dWgwYN1KBBA2VlZekvf/mLGjRoIJfL5fMaf+ynHNnBeW3fvp3D2f9hjNGUKVO0evVqZWZmqkOHDud9Tf/+/fXWW295jW3YsEF9+vRReHh4bbUaEqqzPcvCPlo+Y4yKiorKXMa+WT0VbdOysH+eMWzYMH322WdeY5MmTVLnzp01Y8YM2e12n9f4ZT+t4gXUCDHHjh0z27dvN9u3bzeSzLx588z27dvN/v37jTHGzJw50/z617/21M+fP9+sXr3afPnll2bXrl1m5syZRpJ54403AvURgspdd91lHA6HyczMNHl5eZ7HyZMnPTXnbtOvv/7aNGrUyEybNs3s2bPHPP/88yY8PNysWrUqEB8hqFRne7KPlm/WrFnm/fffN/v27TM7d+40999/vwkLCzMbNmwwxrBvVkdVtyn7Z9WdezdWbeynhB2LK70N8tzHhAkTjDHGTJgwwQwePNhTP2fOHHPxxRebhg0bmubNm5uBAweadevWBab5IFTWtpRkli5d6qk5d5saY0xmZqbp1auXiYiIMO3btzeLFi2q28aDVHW2J/to+W699VbTrl07ExERYVq3bm2GDRvm+VI2hn2zOqq6Tdk/q+7csFMb+6nNmP9c5QMAAGBBXKAMAAAsjbADAAAsjbADAAAsjbADAAAsjbADAAAsjbADAAAsjbADAAAsjbADAAAsjbADAAAsjbADIOSUlJRo6dKlGj9+vJ566qlAtwMgyPFzEQBCSl5ensaMGaM5c+bIbrfryiuv1OHDh9WsWbNAtwYgSHFkB0BIGT9+vK6++mpdeeWVWrZsmWJiYtSwYcNAtwUgiHFkB0DI+Oijj3TFFVfok08+0c9//nMVFxerpKREUVFRgW4NQBDjyA6AkPHWW2+pUaNG6tGjhyQpPDy8zKBz0003qXnz5kpNTS1zPWvWrFFaWlpttgogiBB2AISMrKwsDRw4UA0aNKiw7t5779Xf//73cpfv3LnTE5gAWB9hB0BIOHXqlHJycjRs2LDz1g4ZMkRNmjQpdzlhB6hfCDsAQsJHH32k4uJijR492meZy+Wq0rp2796trl27+qs1AEGOsAMgJPzP//yPLrvsMs2dO1dTpkzRtm3bJEnbt2/XU089pcrea3Hy5EmFhYVxUTNQjxB2AAS9Tz75RNu2bVPXrl21a9cuvf766xo4cKCGDRumL774Qvfee69sNlul1rVr1y6O6gD1TMVX+QFAEOjdu7fWrVvnl3VxvQ5Q/3BkB4DlXHPNNRo9erTefvttxcfHKycnx7Pss88+I+wA9QyTCgIAAEvjyA4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALA0wg4AALC0/wd4wxxpcE4WLgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_global_symmetric_coupling(cross_average_path_length, \"cross-average path length\", r\"\\mathcal{L}\")"]}, {"cell_type": "markdown", "id": "f87b8bdf-6188-4896-b258-d08409b209c8", "metadata": {}, "source": ["#### Global Cross-Clustering Coefficient"]}, {"cell_type": "code", "execution_count": 12, "id": "24355550-7635-4f2f-a301-804369555685", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_global_asymmetric_coupling(np.array(cross_global_clustering), \"global cross-clustering\", r\"\\mathcal{C}\")"]}, {"cell_type": "markdown", "id": "1e334508-8705-489b-bdfa-a7f190850486", "metadata": {}, "source": ["#### Cross-Transitivity"]}, {"cell_type": "code", "execution_count": 13, "id": "a660fabf-8672-48e4-8488-6a6b3d6a071b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_global_asymmetric_coupling(np.array(cross_transitivity), \"cross-transitivity\", r\"\\mathcal{T}\")"]}, {"cell_type": "markdown", "id": "711d6aef", "metadata": {}, "source": ["### Local Measures"]}, {"attachments": {}, "cell_type": "markdown", "id": "401c42a2-7077-4ce4-8654-4aa22a10132b", "metadata": {}, "source": ["For visualising the three-dimensional fields of local cross-network measures $m^{ij}_{v(\\vartheta,\\phi)}$, where $\\vartheta$ and $\\phi$ denote latitude and longitude, we choose to focus on their variation with height and latitude, and therefore consider zonal averages along circles of constant latitude,\n", "$$ m^{ij}(\\vartheta)=\\langle m^{ij}_{v(\\vartheta,\\phi)} \\rangle _\\phi \\,. $$\n", "\n", "When computing local asymmetric cross-network measures for two subnetworks $G_i$ and $G_j$, `climate.CoupledClimateNetwork` returns values for all vertices $v$ of the coupled network in form of a pair of Numpy arrays, i.e., tuples $(m_{\\,\\cdot}^{ij},m_{\\,\\cdot}^{ji})$ where $m_{\\,\\cdot}^{ij}$ is a 1-D Numpy array ordered by latitude *and* longitude. In order to select the measures of the nodes by latitude and longitude, we need to reshape the output array into a $N_{\\vartheta} \\times N_{\\phi}$ matrix, with $N_{\\vartheta}$/$N_{\\phi}$ being the number of latitude/longitude values. In our example, these numbers are the same for all 17 generated coupled climate networks."]}, {"cell_type": "code", "execution_count": 14, "id": "aac4fd1e-1fae-48c7-9d26-692bf5a7a05c", "metadata": {}, "outputs": [], "source": ["from typing import List, Tuple, Callable, Optional\n", "\n", "lat, lon = [\n", "    np.unique(coupled_network.grid_1.__getattribute__(f\"{coo}_sequence\")())\n", "    for coo in [\"lat\", \"lon\"]]\n", "X, Y = np.meshgrid(lat, hgt_averaged)\n", "\n", "def plot_zonal_coupling(\n", "    measure: List[<PERSON><PERSON>[np.n<PERSON><PERSON>,...]], title: str, clabel: str,\n", "    vert_labels: Tuple[str, str] = tuple(\n", "        [f\"pointing {v}wards\" for v in ['up', 'down']]),\n", "    vert_indices: Tuple[str, str] = (r\"^{\\,1l}\", r\"^{\\,l1}\"),\n", "    transform: Optional[Callable[[np.ndarray], np.ndarray]] = None):\n", "    \"\"\"\n", "    Zonal heat plot of a coupling network measure.\n", "    \"\"\"\n", "    fig, axes = plt.subplots(1, 2, layout=\"constrained\")\n", "    fig.suptitle(title)\n", "    Z = np.zeros((VERTICAL_LEVELS, len(lat)))\n", "    \n", "    # plot both vertical directions of coupling\n", "    for vert in range(2):\n", "        # average over lon with same lat\n", "        for l in range(VERTICAL_LEVELS):\n", "            Z[l] = measure[l][vert].reshape(len(lat), len(lon)).mean(axis=1)\n", "        Z = Z if transform is None else transform(Z)\n", "        ax = axes[vert]\n", "        pcm = ax.pcolormesh(X, Y, Z)\n", "        ax.set_title(vert_labels[vert])\n", "        ax.set_xlabel(r\"Latitude $\\vartheta$ (°N)\")\n", "        if vert == 0:\n", "            ax.set_ylabel(r\"$Z_{\\,l}$ (km)\")\n", "        fig.colorbar(\n", "            pcm, ax=ax,\n", "            label=r\"${}\".format(clabel) + vert_indices[vert] + r\"(\\vartheta)$\")"]}, {"cell_type": "markdown", "id": "6bc0c10e", "metadata": {}, "source": ["#### Cross-Degree"]}, {"cell_type": "code", "execution_count": 15, "id": "4d00102f-8f4b-4788-8cd6-99ec9f87ba6f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_zonal_coupling(cross_degree, \"cross-degree\", \"k\")"]}, {"cell_type": "markdown", "id": "a9035fe5", "metadata": {}, "source": ["#### Cross-Closeness"]}, {"cell_type": "code", "execution_count": 16, "id": "31c0412b-736e-472f-b77a-f71e228728f3", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_zonal_coupling(cross_closeness, \"cross-closeness\", \"c\")"]}, {"cell_type": "markdown", "id": "c72290fc", "metadata": {}, "source": ["#### Cross-Betweenness\n", "\n", "In contrast to the previous two local measures, cross-betweenness $b^{ij}_w$ is defined for $w\\in V_i \\cup V_j$ and is *symmetric* w.r.t. exchanging the involved subnetworks. Therefore, in the following we analyse the zonally averaged fields of cross-betweenness\n", "for vertices taken from a specific isobaric subnetwork $i$,\n", "$$ b^{ij}_i(\\vartheta)=\\langle b^{ij}_{w(\\vartheta,\\phi)}\\rangle_{\\phi,w\\in V_i}\\,.$$"]}, {"cell_type": "code", "execution_count": 17, "id": "80f0b4a6-4505-4e15-b3c3-63208e3f93e6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def clip_log(Z):\n", "    neg = Z <= 1\n", "    return np.where(neg, 0, np.log10(Z, where=np.logical_not(neg)))\n", "\n", "plot_zonal_coupling(\n", "    cross_betweenness, \"cross-betweenness\", \"log_{10}b\",\n", "    vert_labels=(\"near ground\", \"upper level\"),\n", "    vert_indices=(r\"_{\\,1}^{\\,1l}\", r\"_{\\,l}^{\\,1l}\"),\n", "    transform=clip_log)"]}], "metadata": {"kernelspec": {"display_name": "pyunicorn312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}