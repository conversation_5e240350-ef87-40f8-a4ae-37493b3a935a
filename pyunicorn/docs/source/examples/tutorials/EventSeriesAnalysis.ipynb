{"cells": [{"cell_type": "markdown", "id": "12877375", "metadata": {}, "source": ["# Tutorial: Event Series Analysis "]}, {"cell_type": "markdown", "id": "efc240da", "metadata": {}, "source": ["*Originally written as part of a PhD thesis in Physics by <PERSON> (<EMAIL>) at the Potsdam Institute of Climate Impact Research (PIK) and Humboldt University Berlin.*\n", "\n", "---"]}, {"cell_type": "markdown", "id": "f2f2eb59", "metadata": {}, "source": ["synchronization measures of time series have been attracting attention in several research areas, including climatology and neuroscience.\n", "synchronization can be understood as a measure of interdependence or strong correlation between time series. \n", "The main use cases of synchronization are:\n", "- Quantification of similarities in phase space between two time series\n", "- Quantification of differences in phase space between two time series.\n", "\n", "A research example of synchronization phenomena is the analysis of electroencephalographic (EEG) signals as a major influencing factor to understand the communication within the brain, see [<PERSON><PERSON><PERSON><PERSON> et al. (2001)](https://journals.aps.org/pre/abstract/10.1103/PhysRevE.65.041903).\n", "\n", "Two widely accepted measurement methods of synchronization are **Event Synchronization (ES)** and **Event Coincidence Analysis (ECA)**. The non-linear nature of these two methods makes them widely applicable for a wide range of utilizations. \n", "While ES does not include the difference in timescales when measuring synchrony, when using ECA a certain timescale has to be selected for analysis purposes.\n", "For more background information consult [<PERSON><PERSON><PERSON><PERSON> et al. (2020)](https://journals.aps.org/pre/abstract/10.1103/PhysRevE.101.052213) and [<PERSON><PERSON><PERSON><PERSON> et al. (2001)](https://journals.aps.org/pre/abstract/10.1103/PhysRevE.66.041904)."]}, {"cell_type": "markdown", "id": "5424b801", "metadata": {}, "source": ["## Event Synchronization (ES)"]}, {"cell_type": "markdown", "id": "a0721685", "metadata": {}, "source": ["As mentioned before, the parameter-free method ES offers a fast and reliable way to measure synchronization between time series.\n", "The fundamental idea is illustrated by the picture below ([<PERSON><PERSON><PERSON><PERSON> et al., 2020](https://journals.aps.org/pre/abstract/10.1103/PhysRevE.101.052213)):\n", "\n", "![Event synchronization](./images/EventSynchronisation.png)\n", "\n", "Two events $l$ and $m$ from timeseries $i$ and $j$, respectively, are considered synchronous if they occur within a certain time interval $\\tau$ which is determined from the data properties. The time interval $\\tau$ is defined as:\n", "\n", "$$\\tau_{lm}^{ij}=\\frac{1}{2}\\min\\left\\{t_{l+1}^{i}-t_{l}^{i}, \\; t_{l}^{i}-t_{l-1}^{i}, \\; t_{m+1}^{j}-t_{m}^{j}, \\; t_{m}^{j}-t_{m-1}^{j}\\right\\}$$\n", "\n", "Thus, given an event in timeseries $j$, the occurrences of synchronized events in $i$ can be counted as\n", "\n", "$$c(i|j)=\\sum_{l=2}^{s_i-1}\\sum_{m=2}^{s_j-1}J_{lm}^{ij} \\,,$$\n", "\n", "where $J_{lm}^{ij}$ counts the events that match the synchronization condition.\n", "Finally, we can define the strength of event synchronization between the timeseries $i$ and $j$ by\n", "\n", "$$Q_{ij}^{ES}=\\frac{c(i|j)+c(j|i)}{\\sqrt{(s_i-2)(s_j-2)}}\\,.$$\n", "\n", "In the usual case, when the timeseries are not fully synchronized, $0 \\le Q_{ij}^{ES} \\le 1$, and total or absent synchronization correspond to $Q_{ij}^{ES} = 1$ or $Q_{ij}^{ES} = 0$, respectively.\n", "To generate an undirected network from a set of timeseries, we can consider the values $Q_{ij}^{ES}$ as the coefficients of a square symmetric matrix $Q^{ES}$. It should be noted that fully synchronized time series will adapt a value of $Q_{ii}^{ES} = Q_{jj}^{ES} = 1$.\n", "\n", "The advantage of ES is that no parameters, such as a delay specification between the two timeseries, has to selected *a priori*."]}, {"cell_type": "markdown", "id": "1efab43e", "metadata": {}, "source": ["## Event Coincidence Analysis (ECA)"]}, {"cell_type": "markdown", "id": "fae1001e", "metadata": {}, "source": ["In contrast, ECA incorporates *static (global) coincidence intervals*. For a chosen tolerance interval $\\Delta T$, an *instantaneous event coincidence* between two events $t_{m}^{j} < t_{l}^{i}$ is defined by the condition\n", "$0 \\leq t_{l}^{i} -  t_{m}^{j} \\leq \\Delta T$, and is generalized to a *lagged event coincidence* via a *time lag* $\\tau \\ge 0$ on timeseries $i$. The fundamental idea is illustrated by the picture below ([<PERSON><PERSON><PERSON><PERSON> et al., 2020](https://journals.aps.org/pre/abstract/10.1103/PhysRevE.101.052213)):\n", "\n", "![ECA](images/ECA.png)\n", "\n", "When computing the coincidence rate with ECA, the *precursor* and *trigger* event coincidence rates should be distinguished. The former refers to events in $i$ that precede all events in $j$, and the latter referes to events in $j$ that precede at least one event in $i$. More precisely, the precursor event coincidence rate is defined as\n", "\n", "$$r_p(i|j;\\Delta T,\\tau) = \\frac{1}{s_i-s_{i}'}\\sum_{l=1+s_i'}^{s_i} \\Theta \\left[\\sum_{m=1}^{s_j} 1_{[0,\\Delta T]}\\left[(t_l^i-\\tau)-t_m^j\\right]\\right] \\,,$$\n", "\n", "and the trigger event coincidence rate as\n", "\n", "$$r_p(i|j;\\Delta T,\\tau)=\\frac{1}{s_j-s_j''}\\sum_{m=1}^{s_j-s_j''}\\Theta\\left[\\sum_{l=1}^{s_i} 1_{[0,\\Delta T]} \\left[(t_l^i-\\tau)-t_m^j\\right]\\right] \\,.$$\n", "\n", "For details on the calculation of $s_i', s_j''$, consult [<PERSON><PERSON><PERSON><PERSON> et al. (2020)](https://journals.aps.org/pre/abstract/10.1103/PhysRevE.101.052213).\n", "By changing the indices in the precursor or trigger rate, one obtains the opposite rates, e.g., $r_t(j|i; \\Delta T, \\tau)$. Therefore, the ECA yields a total of four coincidence rates.\n", "\n", "By computing the mean or the maximum of the two directed trigger coincidence rates $r_t(i|j; \\Delta T,\\tau)$ and $r_t(j|i;\\Delta;T,\\tau)$,\n", "one arrives at the *degree of event synchrony* $Q_{ij}^{ECA, mean}$ or $Q_{ij}^{ECA, max}$,\n", "which can be used as a statistical measure of uni- or bidirectional dependency."]}, {"cell_type": "markdown", "id": "8a82f258", "metadata": {}, "source": ["## ES/ECA for Simple Random Event Series"]}, {"attachments": {}, "cell_type": "markdown", "id": "6e06f934-ec77-4034-a57b-8da719b5bb1a", "metadata": {}, "source": ["`pyunicorn` provides a class for ES and ECA. In addition, a method is included for the generation of binary event series from continuous timeseries data. First, we import the necessary packages."]}, {"cell_type": "code", "execution_count": 1, "id": "82ab6709", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from pyunicorn.eventseries import EventSeries"]}, {"cell_type": "markdown", "id": "1274c566-566b-41a0-a7b5-5a925ce824ea", "metadata": {}, "source": ["### Input: Event Matrix"]}, {"attachments": {}, "cell_type": "markdown", "id": "1c7c6d9c-4348-4588-af02-55b630044382", "metadata": {}, "source": ["Next, we initialize the `EventSeries` class with a toy event matrix, in which the first axis represents the timesteps and the second axis covers the variables. Each variable at a specific timestep is either `1` if an event occurred or `0` if it did not."]}, {"cell_type": "code", "execution_count": 2, "id": "1be1ea26-cdb7-44c5-9a7f-00d1adf7dcff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EventSeries: 2 variables, 10 timesteps, taumax: 1.0, lag: 0.0\n"]}], "source": ["series = np.array([[0, 0],\n", "                   [1, 1],\n", "                   [0, 0],\n", "                   [1, 1],\n", "                   [0, 0],\n", "                   [0, 0],\n", "                   [1, 0],\n", "                   [0, 1],\n", "                   [0, 0],\n", "                   [0, 0]])\n", "\n", "ev = EventSeries(series, taumax=1)\n", "print(ev)"]}, {"cell_type": "markdown", "id": "c350db9f", "metadata": {}, "source": ["**Caution:** The argument `taumax` represents the maximum time difference $\\Delta T$ between two events that are to be considered synchronous. For ES, using the default `taumax=np.inf` is sensible because of its dynamic coincidence interval, whereas for ECA, a finite `taumax` needs to be specified."]}, {"attachments": {}, "cell_type": "markdown", "id": "59b6d71d-4852-4441-8174-f57e8c500cb8", "metadata": {}, "source": ["For variables $X,Y$, the return values of the synchronization analysis methods are:\n", "\n", "- ES: $\\left( Q^{ES}_{XY},\\, Q^{ES}_{YX} \\right)$\n", "- ECA: $\\left( r_p(Y|X),\\, r_t(Y|X),\\, r_p(X|Y),\\, r_t(X|Y) \\right)$."]}, {"cell_type": "code", "execution_count": 3, "id": "9295b844", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0.5, 0.5)\n", "(0.5, 1.0, 1.0, 1.0)\n"]}], "source": ["print(ev.event_synchronization(*series.T))\n", "print(ev.event_coincidence_analysis(*series.T, taumax=1))"]}, {"cell_type": "markdown", "id": "43a272de-8ee9-4734-925a-a45965c70c18", "metadata": {}, "source": ["### Input: Timeseries"]}, {"cell_type": "markdown", "id": "80260d7e", "metadata": {}, "source": ["If the input data is not provided as an event matrix, the constructor tries to generate one from continuous time series data using the `make_event_matrix()` method. Therefore, the argument `threshold_method` needs to be specified along with the argument `threshold_values`. `threshold_method` can be `'quantile'`,  `'value'`, or a 1D Numpy array with entries `'quantile'` or `'value'` for each variable. If `'value'` is selected, one has to specify a number lying in the range of the array; for `'quantile'`, a number between 0 and 1 has to be selected since it specifies the fraction of the array's values which should be included in the event matrix. Additionally, one can specify the argument `threshold_type`, if the threshold should be applied `'above'` or `'below'` the specified `threshold_method`.\n", "\n", "Here is a simple example for finding the synchrony between two continuous time series variables:"]}, {"cell_type": "code", "execution_count": 4, "id": "aca1e9f0-1892-42e0-ae31-13321c30589b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[8 7]\n", " [0 3]\n", " [2 5]\n", " [9 7]\n", " [3 6]\n", " [9 9]\n", " [8 1]\n", " [9 2]\n", " [8 8]\n", " [3 5]]\n", "EventSeries: 2 variables, 10 timesteps, taumax: 1.0, lag: 0.0\n"]}], "source": ["series = (10 * np.random.rand(10, 2)).astype(int)\n", "ev = EventSeries(series, threshold_method='quantile',\n", "                 threshold_values=0.5, threshold_types='below', taumax=1)\n", "print(series); print(ev)"]}, {"cell_type": "code", "execution_count": 5, "id": "c92101e6-85e5-4dad-84c1-55a3d9331a62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ES: (0.46770717334674267, 0.46770717334674267)\n", "ECA: (1.0, 1.0, 1.0, 1.0)\n", "ECA[p]: (1.0, 1.0)\n"]}], "source": ["print(\"ES:\", ev.event_synchronization(*series.T))\n", "print(\"ECA:\", ev.event_coincidence_analysis(*series.T, taumax=1))\n", "# only compute the precursor event coincidence rates\n", "print(\"ECA[p]:\", ev._eca_coincidence_rate(*series.T, window_type='advanced'))"]}, {"cell_type": "markdown", "id": "3ad4d332-4909-4fa9-9ed7-8539decd7ff9", "metadata": {}, "source": ["### Output: Event Series Measure / Functional Network"]}, {"attachments": {}, "cell_type": "markdown", "id": "8b13dcec-2393-439e-94c9-c261d489760a", "metadata": {}, "source": ["The `event_series_analysis()` method, with its argument `method` set to `ES` or `ECA`, can be used to construct a measure of dependence (*functional network*) as described in the introduction. Such matrices can then be subjected to complex network theory methods as a part of non-linear timeseries analysis, see **Tutorial: Recurrence Networks**.\n", "\n", "The return value is a $N\\!\\times\\! N$ matrix, where $N$ is the number of variables. For detailed information on the calculation of the matrix and the required arguments, please consult the API documentation."]}, {"cell_type": "code", "execution_count": 6, "id": "0f5cf4b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ES:\n", "[[0.         0.20412415]\n", " [0.20412415 0.        ]]\n", "ECA:\n", "[[0.         0.41666667]\n", " [0.41666667 0.        ]]\n"]}], "source": ["matrix_ES = ev.event_series_analysis(method='ES')\n", "print(\"ES:\"); print(matrix_ES)\n", "matrix_ECA = ev.event_series_analysis(method='ECA', symmetrization='mean', window_type='advanced')\n", "print(\"ECA:\"); print(matrix_ECA)"]}, {"cell_type": "markdown", "id": "6376d465", "metadata": {}, "source": ["### Significance Level Calculations"]}, {"cell_type": "markdown", "id": "d28b190a", "metadata": {}, "source": ["The signifcance levels of event synchronization can also be calculated using `pyunicorn`. The methods `_empirical_percentiles()` and `event_analysis_significance()` respectively estimate the $p$-values via a Monte Carlo approach and the signifcance levels ($1 - p$) via a Poisson process. For detailed information please consult the API documentation."]}, {"cell_type": "code", "execution_count": 7, "id": "4e4dc503-53c9-4aad-9f32-c5b05569642c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MC[ES]:\n", "[[0.     0.2726]\n", " [0.271  0.    ]]\n", "MC[ECA]:\n", "[[0.     0.0519]\n", " [0.0519 0.    ]]\n", "Poisson[ES]:\n", "[[0.     0.2782]\n", " [0.2785 0.    ]]\n", "Poisson[ECA]:\n", "[[0.     0.0516]\n", " [0.0516 0.    ]]\n"]}], "source": ["print(\"MC[ES]:\")\n", "print(ev._empirical_percentiles(method='ES', n_surr=int(1e4)))\n", "print(\"MC[ECA]:\")\n", "print(ev._empirical_percentiles(method='ECA', n_surr=int(1e4),\n", "                                symmetrization='mean', window_type='advanced'))\n", "print(\"Poisson[ES]:\")\n", "print(ev.event_analysis_significance(method='ES', n_surr=int(1e4)))\n", "print(\"Poisson[ECA]:\")\n", "print(ev.event_analysis_significance(method='ECA', n_surr=int(1e4),\n", "                                     symmetrization='mean', window_type='advanced'))"]}, {"cell_type": "markdown", "id": "cb3c47de", "metadata": {}, "source": ["## ES/ECA for Generating a Climate Network"]}, {"attachments": {}, "cell_type": "markdown", "id": "7211ee7d-e2b3-4e6d-bf72-3b8c2dbe50fa", "metadata": {}, "source": ["A possible further application of ES and ECA is the generation of climate networks from the event series measures above, and is implemented in the `EventSeriesClimateNetwork` class.\n", "\n", "**Note:** If more general applications of event series networks are desired, use the `EventSeries` class together with the `Network` class."]}, {"cell_type": "code", "execution_count": 8, "id": "ea03f3ea", "metadata": {}, "outputs": [], "source": ["from pyunicorn.core import Data\n", "from pyunicorn.climate.eventseries_climatenetwork import EventSeriesClimateNetwork"]}, {"cell_type": "markdown", "id": "c33df5f1", "metadata": {}, "source": ["We shall use the small test climate dataset provided by `Data`."]}, {"cell_type": "code", "execution_count": 9, "id": "9b8c2e94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data: 6 grid points, 60 measurements.\n", "Geographical boundaries:\n", "         time     lat     lon\n", "   min    0.0    0.00    2.50\n", "   max    9.0   25.00   15.00\n", "\n", "[[ 0.00000000e+00  1.00000000e+00  1.22464680e-16 -1.00000000e+00\n", "  -2.44929360e-16  1.00000000e+00]\n", " [ 3.09016994e-01  9.51056516e-01 -3.09016994e-01 -9.51056516e-01\n", "   3.09016994e-01  9.51056516e-01]\n", " [ 5.87785252e-01  8.09016994e-01 -5.87785252e-01 -8.09016994e-01\n", "   5.87785252e-01  8.09016994e-01]\n", " [ 8.09016994e-01  5.87785252e-01 -8.09016994e-01 -5.87785252e-01\n", "   8.09016994e-01  5.87785252e-01]\n", " [ 9.51056516e-01  3.09016994e-01 -9.51056516e-01 -3.09016994e-01\n", "   9.51056516e-01  3.09016994e-01]\n", " [ 1.00000000e+00  1.22464680e-16 -1.00000000e+00 -2.44929360e-16\n", "   1.00000000e+00  3.67394040e-16]\n", " [ 9.51056516e-01 -3.09016994e-01 -9.51056516e-01  3.09016994e-01\n", "   9.51056516e-01 -3.09016994e-01]\n", " [ 8.09016994e-01 -5.87785252e-01 -8.09016994e-01  5.87785252e-01\n", "   8.09016994e-01 -5.87785252e-01]\n", " [ 5.87785252e-01 -8.09016994e-01 -5.87785252e-01  8.09016994e-01\n", "   5.87785252e-01 -8.09016994e-01]\n", " [ 3.09016994e-01 -9.51056516e-01 -3.09016994e-01  9.51056516e-01\n", "   3.09016994e-01 -9.51056516e-01]]\n"]}], "source": ["data = Data.SmallTestData()\n", "print(data); print()\n", "print(data.observable())"]}, {"cell_type": "code", "execution_count": 10, "id": "e591bcf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "EventSeriesClimateNetwork:\n", "EventSeries: 6 variables, 10 timesteps, taumax: 16.0, lag: 0.0\n", "ClimateNetwork:\n", "GeoNetwork:\n", "SpatialNetwork:\n", "Network: directed, 6 nodes, 0 links, link density 0.000.\n", "Geographical boundaries:\n", "         time     lat     lon\n", "   min    0.0    0.00    2.50\n", "   max    9.0   25.00   15.00\n", "Threshold: 0\n", "Local connections filtered out: False\n", "Type of event series measure to construct the network: directedES\n"]}], "source": ["climate_ES = EventSeriesClimateNetwork(\n", "    data, method='ES', taumax=16.0,\n", "    threshold_method='quantile', threshold_values=0.8, threshold_types='above')\n", "print(climate_ES)"]}, {"cell_type": "code", "execution_count": 11, "id": "bbb03017", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting network adjacency matrix by thresholding...\n", "Setting area weights according to type surface ...\n", "Setting area weights according to type surface ...\n", "EventSeriesClimateNetwork:\n", "EventSeries: 6 variables, 10 timesteps, taumax: 16.0, lag: 0.0\n", "ClimateNetwork:\n", "GeoNetwork:\n", "SpatialNetwork:\n", "Network: directed, 6 nodes, 0 links, link density 0.000.\n", "Geographical boundaries:\n", "         time     lat     lon\n", "   min    0.0    0.00    2.50\n", "   max    9.0   25.00   15.00\n", "Threshold: 0\n", "Local connections filtered out: False\n", "Type of event series measure to construct the network: directedECA\n"]}], "source": ["climate_ECA = EventSeriesClimateNetwork(\n", "    data, method='ECA', taumax=16.0,\n", "    threshold_method='quantile', threshold_values=0.8, threshold_types='above')\n", "print(climate_ECA)"]}], "metadata": {"kernelspec": {"display_name": "pyunicorn", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}