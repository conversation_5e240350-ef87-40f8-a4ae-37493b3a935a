#! /usr/bin/env bash
#
# This file is part of pyunicorn.
# Copyright (C) 2008--2025 Jonathan <PERSON> and pyunicorn authors
# URL: <https://www.pik-potsdam.de/members/donges/software-2/software>
# License: BSD (3-clause)
#
# Please acknowledge and cite the use of this software and its authors
# when results are used in publications or published elsewhere.
#
# You can use the following reference:
# <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, J. <PERSON>, Q.-<PERSON><PERSON>,
# <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
# and <PERSON><PERSON>, "Unified functional network and nonlinear time series analysis
# for complex systems science: The pyunicorn package"


# default configuration
first_year=2008
old_last_year=""
new_last_year=""

# evaluate arguments
if !(( $# == 1 || $# == 2 )); then
    echo "Expecting 1 or 2 arguments:"
    echo "   old_last_year [new_last_year]"
    echo "   (int)         (int)"
    exit 1
fi;
old_last_year=$(($1))
if (( $# == 2 )); then
    new_last_year=$(($2)); else
    new_last_year=$(($old_last_year + 1))
fi;

# determine absolute path of repository
repo="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"; cd $repo

# update copyright notices
grep -rlIE --exclude-dir=".git" --exclude-dir=".tox" --exclude-dir="build" \
    "$first_year-+$old_last_year" \
    | xargs sed -i"" -E -e "s:($first_year-+)$old_last_year:\1$new_last_year:g"
