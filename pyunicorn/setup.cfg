# This file is part of pyunicorn.
# Copyright (C) 2008--2025 Jonathan <PERSON> and pyunicorn authors
# URL: <https://www.pik-potsdam.de/members/donges/software-2/software>
# License: BSD (3-clause)

# package ======================================================================

[metadata]
name = pyunicorn
version = 0.8.2
author = <PERSON>
author_email = <EMAIL>
description = Unified complex network and recurrence analysis toolbox
long_description = file: README.rst
long_description_content_type = text/x-rst
keywords =
    complex networks statistics modeling time series analysis
    nonlinear climate recurrence plot surrogates spatial model
license = BSD
license_files = LICENSE.txt
url = https://www.pik-potsdam.de/members/donges/software-2/software
project_urls =
    Documentation = http://www.pik-potsdam.de/~donges/pyunicorn/
    Source Code = https://github.com/pik-copan/pyunicorn
    Issue Tracker = https://github.com/pik-copan/pyunicorn/issues
platforms = all
classifiers =
    Development Status :: 5 - Production/Stable
    Environment :: Console
    Intended Audience :: Science/Research
    License :: OSI Approved :: BSD License
    Natural Language :: English
    Operating System :: OS Independent
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Programming Language :: Python :: 3.13
    Topic :: Scientific/Engineering :: GIS
    Topic :: Scientific/Engineering :: Information Analysis
    Topic :: Scientific/Engineering :: Mathematics
    Topic :: Scientific/Engineering :: Physics

[options]
install_requires =
    numpy >= 1.24
    scipy >= 1.10
    igraph >= 0.11
    h5netcdf >= 1.1
    tqdm >= 4.66
python_requires = >=3.9
packages = find:
package_dir =
    = src
include_package_data = true
zip_safe = false

[options.packages.find]
where = src
include = pyunicorn*

[options.extras_require]
dev =
    Cython >= 3.0
tests =
    matplotlib >= 3.6
    cartopy >= 0.21
    networkx >= 3.1
    tox >= 4.11
    flake8 >= 7.0
    pylint >= 3.0
    pytest >= 8.0
    pytest-xdist >= 3.5
    pytest-cov >= 4.1
docs =
    sphinx >= 7.0
    nbsphinx >= 0.9.3
    ipython >= 8.4
    pandoc >= 2.3
    matplotlib >= 3.6

# test suite ===================================================================

[tox:tox]
requires =
    setuptools >= 65
isolated_build = false
usedevelop = false
envlist =
    style
    lint
    test
    docs

[testenv]
skip_install = true
skipsdist = true
sitepackages = true
changedir = {toxinidir}
setenv =
    PYTHONPATH = {toxinidir}/src
passenv = WINDIR, LC_ALL
allowlist_externals =
    flake8
    pylint
    pytest
    sphinx-build
    pandoc

[testenv:style]
commands =
    flake8 src/pyunicorn tests

[testenv:lint]
commands =
    pylint src/pyunicorn tests

[testenv:test]
extras = tests
commands =
    pytest --cov

[testenv:docs]
extras = docs
commands =
    sphinx-build -v -j 8 -W -b html -d {envtmpdir}/doctrees docs/source {envtmpdir}/html

# test tools ===================================================================

[flake8]
extend-exclude =
    .git, .cache, .tox, .ropeproject, build, docs/source/conf.py
per-file-ignores =
    */__init__.py:F401,F403
