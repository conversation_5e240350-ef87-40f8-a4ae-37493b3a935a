
Contributions
=============

Copyright
---------
\ |copy| 2008-2025 Jonathan <PERSON> and pyunicorn authors.

License
-------
BSD (3-clause)

URL
---
https://www.pik-potsdam.de/members/donges/software-2/software

Mail
----
<PERSON>, Potsdam Institute for Climate Impact Research,
P.O. Box 60 12 03, D-14412 Potsdam, Germany

Related publications
--------------------
See `Publications <docs/source/publications.rst>`_.

Authors
-------
Written as part of a diploma/PhD thesis in physics by `<PERSON>
<<EMAIL>>`_ at Humboldt University Berlin and the Potsdam
Institute for Climate Impact Research (PIK) and completed at the University of
Potsdam, Germany. Substantially extended by `<PERSON><PERSON>
<<EMAIL>>`_.

Contributors
------------
- <PERSON> (extended ``core`` and ``climate``)
- <PERSON>
- <PERSON>
- <PERSON> (extended ``core`` and ``climate``)
- `<PERSON><PERSON><PERSON> <<EMAIL>>`_
  (extended ``timeseries`` during an internship at PIK)
- `<PERSON> <<EMAIL>>`_ (extended ``timeseries``)
- Aljoscha Rheinwalt
- Hannes Kutza
- `Boyan Beronov <<EMAIL>>`_ (restructured, updated and linted
  codebase and documentation, consolidated original packaging, prepared
  Cythonization and migration to Python 3, managed open-sourcing, introduced CI,
  migrated to PEP 517/518 package format, overhauled the Python/Cython interface,
  made Cython/C extensions compatible with MSVC, edited tutorials,
  overhauled the caching system, maintained test suite and CI)
- `Paul Schultz <<EMAIL>>`_, `Stefan Schinkel
  <<EMAIL>>`_ (added ``resistive_network`` and corresponding
  tests)
- `Wolfram Barfuss <<EMAIL>>`_
  (contributed to Cythonization, extended and maintained package)
- Malte Ziehbarth (contributed to Python 3 support in
  `#106 <https://github.com/pik-copan/pyunicorn/pull/106>`_)
- Nils Harmening (added event coincidence analysis, contributed to Cythonization,
  extended test suite, migrated from Python 2.7 to 3.6)
- Jonathan Kroenke (fixed numerous bugs and style issues in
  `#119 <https://github.com/pik-copan/pyunicorn/pull/119>`_,
  generalized spatial and interacting network analysis in
  `#131 <https://github.com/pik-copan/pyunicorn/pull/131>`_,
  extended test suite, maintained package and managed release)
- Johannes Kassel (added ``eventseries`` and
  ``climate.eventseries_climatenetwork`` in
  `#156 <https://github.com/pik-copan/pyunicorn/pull/156>`_)
- `Frederik Wolf <<EMAIL>>`_ (contributed to ``eventseries``)
- Lena Schmidt (added tutorials, maintained package)
- `Max Bechthold <<EMAIL>>`_
  (reenabled CI, migrated plotting to ``Cartopy``, added tutorials,
  maintained package)
- `Ronja Hotz <<EMAIL>>`_ (added a tutorial
  in `#190 <https://github.com/pik-copan/pyunicorn/pull/190>`_)
- `Fritz Kühlein <<EMAIL>>`_
  (fixed numerous bugs and style issues, improved test coverage and CI,
  integrated tutorial notebooks into documentation,
  maintained package and managed releases)

Acknowledgements
----------------
- Travis-CI (https://www.travis-ci.com/) for providing free builds for this open
  source project.

.. |copy|   unicode:: U+000A9 .. COPYRIGHT SIGN
